{"version": 3, "file": "requestdata.js", "sources": ["../../../src/integrations/requestdata.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { Event } from '../types-hoist/event';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport type { RequestEventData } from '../types-hoist/request';\nimport { parseCookie } from '../utils/cookie';\nimport { getClientIPAddress, ipHeaderNames } from '../vendor/getIpAddress';\n\ninterface RequestDataIncludeOptions {\n  cookies?: boolean;\n  data?: boolean;\n  headers?: boolean;\n  ip?: boolean;\n  query_string?: boolean;\n  url?: boolean;\n}\n\ntype RequestDataIntegrationOptions = {\n  /**\n   * Controls what data is pulled from the request and added to the event.\n   */\n  include?: RequestDataIncludeOptions;\n};\n\n// TODO(v10): Change defaults based on `sendDefaultPii`\nconst DEFAULT_INCLUDE: RequestDataIncludeOptions = {\n  cookies: true,\n  data: true,\n  headers: true,\n  query_string: true,\n  url: true,\n};\n\nconst INTEGRATION_NAME = 'RequestData';\n\nconst _requestDataIntegration = ((options: RequestDataIntegrationOptions = {}) => {\n  const include = {\n    ...DEFAULT_INCLUDE,\n    ...options.include,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event, _hint, client) {\n      const { sdkProcessingMetadata = {} } = event;\n      const { normalizedRequest, ipAddress } = sdkProcessingMetadata;\n\n      const includeWithDefaultPiiApplied: RequestDataIncludeOptions = {\n        ...include,\n        ip: include.ip ?? client.getOptions().sendDefaultPii,\n      };\n\n      if (normalizedRequest) {\n        addNormalizedRequestDataToEvent(event, normalizedRequest, { ipAddress }, includeWithDefaultPiiApplied);\n      }\n\n      return event;\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add data about a request to an event. Primarily for use in Node-based SDKs, but included in `@sentry/core`\n * so it can be used in cross-platform SDKs like `@sentry/nextjs`.\n */\nexport const requestDataIntegration = defineIntegration(_requestDataIntegration);\n\n/**\n * Add already normalized request data to an event.\n * This mutates the passed in event.\n */\nfunction addNormalizedRequestDataToEvent(\n  event: Event,\n  req: RequestEventData,\n  // Data that should not go into `event.request` but is somehow related to requests\n  additionalData: { ipAddress?: string },\n  include: RequestDataIncludeOptions,\n): void {\n  event.request = {\n    ...event.request,\n    ...extractNormalizedRequestData(req, include),\n  };\n\n  if (include.ip) {\n    const ip = (req.headers && getClientIPAddress(req.headers)) || additionalData.ipAddress;\n    if (ip) {\n      event.user = {\n        ...event.user,\n        ip_address: ip,\n      };\n    }\n  }\n}\n\nfunction extractNormalizedRequestData(\n  normalizedRequest: RequestEventData,\n  include: RequestDataIncludeOptions,\n): RequestEventData {\n  const requestData: RequestEventData = {};\n  const headers = { ...normalizedRequest.headers };\n\n  if (include.headers) {\n    requestData.headers = headers;\n\n    // Remove the Cookie header in case cookie data should not be included in the event\n    if (!include.cookies) {\n      delete (headers as { cookie?: string }).cookie;\n    }\n\n    // Remove IP headers in case IP data should not be included in the event\n    if (!include.ip) {\n      ipHeaderNames.forEach(ipHeaderName => {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete (headers as Record<string, unknown>)[ipHeaderName];\n      });\n    }\n  }\n\n  requestData.method = normalizedRequest.method;\n\n  if (include.url) {\n    requestData.url = normalizedRequest.url;\n  }\n\n  if (include.cookies) {\n    const cookies = normalizedRequest.cookies || (headers?.cookie ? parseCookie(headers.cookie) : undefined);\n    requestData.cookies = cookies || {};\n  }\n\n  if (include.query_string) {\n    requestData.query_string = normalizedRequest.query_string;\n  }\n\n  if (include.data) {\n    requestData.data = normalizedRequest.data;\n  }\n\n  return requestData;\n}\n"], "names": [], "mappings": ";;;;AAuBA;AACA,MAAM,eAAe,GAA8B;AACnD,EAAE,OAAO,EAAE,IAAI;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,OAAO,EAAE,IAAI;AACf,EAAE,YAAY,EAAE,IAAI;AACpB,EAAE,GAAG,EAAE,IAAI;AACX,CAAC;;AAED,MAAM,gBAAA,GAAmB,aAAa;;AAEtC,MAAM,uBAAA,IAA2B,CAAC,OAAO,GAAkC,EAAE,KAAK;AAClF,EAAE,MAAM,UAAU;AAClB,IAAI,GAAG,eAAe;AACtB,IAAI,GAAG,OAAO,CAAC,OAAO;AACtB,GAAG;;AAEH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,MAAM,MAAM,EAAE,qBAAsB,GAAE,EAAG,EAAA,GAAI,KAAK;AAClD,MAAM,MAAM,EAAE,iBAAiB,EAAE,SAAU,EAAA,GAAI,qBAAqB;;AAEpE,MAAM,MAAM,4BAA4B,GAA8B;AACtE,QAAQ,GAAG,OAAO;AAClB,QAAQ,EAAE,EAAE,OAAO,CAAC,EAAG,IAAG,MAAM,CAAC,UAAU,EAAE,CAAC,cAAc;AAC5D,OAAO;;AAEP,MAAM,IAAI,iBAAiB,EAAE;AAC7B,QAAQ,+BAA+B,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,SAAU,EAAC,EAAE,4BAA4B,CAAC;AAC9G;;AAEA,MAAM,OAAO,KAAK;AAClB,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;MACa,sBAAuB,GAAE,iBAAiB,CAAC,uBAAuB;;AAE/E;AACA;AACA;AACA;AACA,SAAS,+BAA+B;AACxC,EAAE,KAAK;AACP,EAAE,GAAG;AACL;AACA,EAAE,cAAc;AAChB,EAAE,OAAO;AACT,EAAQ;AACR,EAAE,KAAK,CAAC,OAAA,GAAU;AAClB,IAAI,GAAG,KAAK,CAAC,OAAO;AACpB,IAAI,GAAG,4BAA4B,CAAC,GAAG,EAAE,OAAO,CAAC;AACjD,GAAG;;AAEH,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;AAClB,IAAI,MAAM,EAAG,GAAE,CAAC,GAAG,CAAC,WAAW,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,cAAc,CAAC,SAAS;AAC3F,IAAI,IAAI,EAAE,EAAE;AACZ,MAAM,KAAK,CAAC,IAAA,GAAO;AACnB,QAAQ,GAAG,KAAK,CAAC,IAAI;AACrB,QAAQ,UAAU,EAAE,EAAE;AACtB,OAAO;AACP;AACA;AACA;;AAEA,SAAS,4BAA4B;AACrC,EAAE,iBAAiB;AACnB,EAAE,OAAO;AACT,EAAoB;AACpB,EAAE,MAAM,WAAW,GAAqB,EAAE;AAC1C,EAAE,MAAM,UAAU,EAAE,GAAG,iBAAiB,CAAC,SAAS;;AAElD,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,IAAI,WAAW,CAAC,OAAQ,GAAE,OAAO;;AAEjC;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC1B,MAAM,OAAO,CAAC,OAAQ,GAAwB,MAAM;AACpD;;AAEA;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACrB,MAAM,aAAa,CAAC,OAAO,CAAC,gBAAgB;AAC5C;AACA,QAAQ,OAAO,CAAC,OAAA,GAAoC,YAAY,CAAC;AACjE,OAAO,CAAC;AACR;AACA;;AAEA,EAAE,WAAW,CAAC,MAAA,GAAS,iBAAiB,CAAC,MAAM;;AAE/C,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE;AACnB,IAAI,WAAW,CAAC,GAAA,GAAM,iBAAiB,CAAC,GAAG;AAC3C;;AAEA,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,UAAU,iBAAiB,CAAC,OAAQ,KAAI,OAAO,EAAE,SAAS,WAAW,CAAC,OAAO,CAAC,MAAM,CAAE,GAAE,SAAS,CAAC;AAC5G,IAAI,WAAW,CAAC,OAAA,GAAU,OAAQ,IAAG,EAAE;AACvC;;AAEA,EAAE,IAAI,OAAO,CAAC,YAAY,EAAE;AAC5B,IAAI,WAAW,CAAC,YAAA,GAAe,iBAAiB,CAAC,YAAY;AAC7D;;AAEA,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;AACpB,IAAI,WAAW,CAAC,IAAA,GAAO,iBAAiB,CAAC,IAAI;AAC7C;;AAEA,EAAE,OAAO,WAAW;AACpB;;;;"}