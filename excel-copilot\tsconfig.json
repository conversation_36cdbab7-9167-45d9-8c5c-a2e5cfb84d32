{
  "compilerOptions": {
    "target": "es2020",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true /* Pula verificação de tipos em arquivos de declaração .d.ts */,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true /* Habilita interoperabilidade entre módulos */,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "downlevelIteration": true,
    "strictNullChecks": true,
    "allowSyntheticDefaultImports": true /* Permitir importações default */,
    "noImplicitAny": true /* Proíbe o uso de 'any' implícito */,
    "noImplicitThis": true /* Levanta erro quando 'this' é implicitamente 'any' */,
    "strictBindCallApply": true /* Verificação mais rigorosa para bind, call e apply */,
    "strictFunctionTypes": true /* Verificação mais rigorosa para atribuição de funções */,
    "noUncheckedIndexedAccess": true /* Adiciona 'undefined' aos tipos indexados */,
    "exactOptionalPropertyTypes": true /* Diferencia undefined de omissão */,
    "paths": {
      "@/*": ["./src/*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts"],
  "exclude": ["node_modules", "desktop-bridge"],
  "ts-node": {
    "transpileOnly": true
  }
}
