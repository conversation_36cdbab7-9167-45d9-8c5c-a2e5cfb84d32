import { logger } from '@/lib/logger';

/**
 * Sistema de resolução de conflitos em tempo real para colaboração
 * Implementa algoritmo de Operational Transformation (OT) simplificado
 */

export interface CellOperation {
  id: string;
  type: 'insert' | 'delete' | 'update' | 'format';
  cellId: string;
  sheetId: string;
  value?: unknown;
  oldValue?: unknown;
  position?: { row: number; col: number };
  timestamp: number;
  userId: string;
  userName: string;
  priority?: number; // Para resolver conflitos por prioridade
}

export interface ConflictResolution {
  operation: CellOperation;
  resolution: 'accept' | 'reject' | 'merge' | 'transform';
  transformedOperation?: CellOperation;
  reason: string;
}

export interface ConflictContext {
  workbookId: string;
  pendingOperations: CellOperation[];
  lastSyncTimestamp: number;
  activeUsers: string[];
}

export class CollaborationConflictResolver {
  private pendingOperations: Map<string, CellOperation[]> = new Map(); // workbookId -> operations
  private operationHistory: Map<string, CellOperation[]> = new Map(); // workbookId -> history
  private readonly MAX_HISTORY_SIZE = 1000;
  private readonly CONFLICT_WINDOW_MS = 5000; // 5 segundos para detectar conflitos

  /**
   * Adiciona uma operação e verifica conflitos
   */
  async addOperation(
    workbookId: string,
    operation: CellOperation
  ): Promise<ConflictResolution> {
    try {
      // Obter operações pendentes para esta planilha
      const pending = this.pendingOperations.get(workbookId) || [];
      
      // Verificar conflitos com operações recentes
      const conflicts = this.detectConflicts(operation, pending);
      
      if (conflicts.length === 0) {
        // Sem conflitos, aceitar operação
        pending.push(operation);
        this.pendingOperations.set(workbookId, pending);
        this.addToHistory(workbookId, operation);
        
        return {
          operation,
          resolution: 'accept',
          reason: 'Nenhum conflito detectado',
        };
      }

      // Resolver conflitos usando estratégia apropriada
      const resolution = await this.resolveConflict(operation, conflicts);
      
      if (resolution.resolution === 'accept' || resolution.resolution === 'transform') {
        const finalOperation = resolution.transformedOperation || operation;
        pending.push(finalOperation);
        this.pendingOperations.set(workbookId, pending);
        this.addToHistory(workbookId, finalOperation);
      }

      return resolution;
    } catch (error) {
      logger.error('Erro na resolução de conflitos', {
        workbookId,
        operationId: operation.id,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      // Em caso de erro, rejeitar operação por segurança
      return {
        operation,
        resolution: 'reject',
        reason: 'Erro interno na resolução de conflitos',
      };
    }
  }

  /**
   * Detecta conflitos entre operações
   */
  private detectConflicts(
    newOperation: CellOperation,
    pendingOperations: CellOperation[]
  ): CellOperation[] {
    const conflicts: CellOperation[] = [];
    const conflictWindow = newOperation.timestamp - this.CONFLICT_WINDOW_MS;

    for (const pending of pendingOperations) {
      // Verificar se as operações estão na janela de conflito
      if (pending.timestamp < conflictWindow) continue;

      // Verificar se afetam a mesma célula
      if (pending.cellId === newOperation.cellId && pending.sheetId === newOperation.sheetId) {
        // Verificar se são de usuários diferentes
        if (pending.userId !== newOperation.userId) {
          conflicts.push(pending);
        }
      }
    }

    return conflicts;
  }

  /**
   * Resolve conflito usando estratégias apropriadas
   */
  private async resolveConflict(
    newOperation: CellOperation,
    conflicts: CellOperation[]
  ): Promise<ConflictResolution> {
    // Estratégia 1: Last Writer Wins (LWW) - mais simples
    const latestConflict = conflicts.reduce((latest, current) => 
      current.timestamp > latest.timestamp ? current : latest
    );

    if (newOperation.timestamp > latestConflict.timestamp) {
      return {
        operation: newOperation,
        resolution: 'accept',
        reason: `Operação mais recente (${newOperation.timestamp} > ${latestConflict.timestamp})`,
      };
    }

    // Estratégia 2: Prioridade por tipo de operação
    const priorityOrder = { update: 3, format: 2, insert: 1, delete: 0 };
    const newPriority = priorityOrder[newOperation.type] || 0;
    const conflictPriority = priorityOrder[latestConflict.type] || 0;

    if (newPriority > conflictPriority) {
      return {
        operation: newOperation,
        resolution: 'accept',
        reason: `Prioridade de operação (${newOperation.type} > ${latestConflict.type})`,
      };
    }

    // Estratégia 3: Transformação para operações compatíveis
    if (this.canTransform(newOperation, latestConflict)) {
      const transformed = this.transformOperation(newOperation, latestConflict);
      return {
        operation: newOperation,
        resolution: 'transform',
        transformedOperation: transformed,
        reason: 'Operação transformada para evitar conflito',
      };
    }

    // Estratégia 4: Merge para valores compatíveis
    if (this.canMerge(newOperation, latestConflict)) {
      const merged = this.mergeOperations(newOperation, latestConflict);
      return {
        operation: newOperation,
        resolution: 'merge',
        transformedOperation: merged,
        reason: 'Operações mescladas',
      };
    }

    // Fallback: Rejeitar operação
    return {
      operation: newOperation,
      resolution: 'reject',
      reason: 'Conflito irreconciliável detectado',
    };
  }

  /**
   * Verifica se operações podem ser transformadas
   */
  private canTransform(op1: CellOperation, op2: CellOperation): boolean {
    // Transformação é possível para operações de formatação
    return op1.type === 'format' || op2.type === 'format';
  }

  /**
   * Transforma uma operação para evitar conflito
   */
  private transformOperation(
    operation: CellOperation,
    conflictingOperation: CellOperation
  ): CellOperation {
    // Implementação simplificada de transformação
    return {
      ...operation,
      id: `${operation.id}_transformed`,
      timestamp: Date.now(),
      priority: (operation.priority || 0) + 1,
    };
  }

  /**
   * Verifica se operações podem ser mescladas
   */
  private canMerge(op1: CellOperation, op2: CellOperation): boolean {
    // Merge é possível para operações de update com valores numéricos
    return (
      op1.type === 'update' &&
      op2.type === 'update' &&
      typeof op1.value === 'number' &&
      typeof op2.value === 'number'
    );
  }

  /**
   * Mescla duas operações compatíveis
   */
  private mergeOperations(
    op1: CellOperation,
    op2: CellOperation
  ): CellOperation {
    // Implementação simplificada de merge (soma valores numéricos)
    const mergedValue = (op1.value as number) + (op2.value as number);
    
    return {
      ...op1,
      id: `${op1.id}_merged_${op2.id}`,
      value: mergedValue,
      timestamp: Math.max(op1.timestamp, op2.timestamp),
    };
  }

  /**
   * Adiciona operação ao histórico
   */
  private addToHistory(workbookId: string, operation: CellOperation): void {
    const history = this.operationHistory.get(workbookId) || [];
    history.push(operation);

    // Limitar tamanho do histórico
    if (history.length > this.MAX_HISTORY_SIZE) {
      history.splice(0, history.length - this.MAX_HISTORY_SIZE);
    }

    this.operationHistory.set(workbookId, history);
  }

  /**
   * Limpa operações antigas
   */
  public cleanupOldOperations(workbookId: string, olderThan: number): void {
    const pending = this.pendingOperations.get(workbookId) || [];
    const filtered = pending.filter(op => op.timestamp > olderThan);
    this.pendingOperations.set(workbookId, filtered);

    const history = this.operationHistory.get(workbookId) || [];
    const filteredHistory = history.filter(op => op.timestamp > olderThan);
    this.operationHistory.set(workbookId, filteredHistory);
  }

  /**
   * Obtém estatísticas de conflitos
   */
  public getConflictStats(workbookId: string): {
    pendingOperations: number;
    historySize: number;
    lastOperation?: CellOperation;
  } {
    const pending = this.pendingOperations.get(workbookId) || [];
    const history = this.operationHistory.get(workbookId) || [];
    const lastOp = history[history.length - 1];

    const result: {
      pendingOperations: number;
      historySize: number;
      lastOperation?: CellOperation;
    } = {
      pendingOperations: pending.length,
      historySize: history.length,
    };

    if (lastOp) {
      result.lastOperation = lastOp;
    }

    return result;
  }

  /**
   * Reset do estado para uma planilha
   */
  public resetWorkbook(workbookId: string): void {
    this.pendingOperations.delete(workbookId);
    this.operationHistory.delete(workbookId);
  }
}

// Instância singleton do resolver de conflitos
export const conflictResolver = new CollaborationConflictResolver();
