'use client';

import { useChat } from 'ai/react';
import { AnimatePresence, motion } from 'framer-motion';
import { Send, Search, BarChart3, FileSpreadsheet, Sparkles, LinkIcon } from 'lucide-react';
import {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback,
  memo,
} from 'react';

import { useCSRF } from '@/components/providers/csrf-provider';
import { Badge, Button, ErrorMessage, Input, ScrollArea, useToast } from '@/components/ui';
import { useLocale } from '@/context-providers/LocaleProvider';
import {
  ANIMATIONS,
  SPACING,
  SIZES,
  BORDERS,
  TYPOGRAPHY,
  COLORS,
  SHADOWS,
} from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

import { ChatMessage } from './chat-message';
import { EmptyState } from './empty-state';
import { ChatInterfaceProps, ChatInterfaceHandle, ExtendedAIMessage } from './types';

// Componente de mensagem memoizado para evitar re-renderizações desnecessárias
const MemoizedChatMessage = memo(ChatMessage);

export const ChatInterface = forwardRef<ChatInterfaceHandle, ChatInterfaceProps>(
  function ChatInterface({ workbookId, onDataUpdated }, ref) {
    const { t } = useLocale();
    const { toast } = useToast();
    const [extendedMessages, setExtendedMessages] = useState<ExtendedAIMessage[]>([]);

    // Adicionar hook externo para tratamento de erros
    const chatErrorHandler = useCallback(
      (errorMessage: string) => {
        toast({
          title: t('requestError'),
          description: errorMessage,
          variant: 'destructive',
        });
      },
      [toast, t]
    );

    // Usar hook de gerenciamento de erros simplificado
    const [error, setError] = useState<string | null>(null);
    const clearError = useCallback(() => setError(null), []);
    const handleError = useCallback((err: string) => setError(err), []);

    // Status de conexão do Excel (simulado para compatibilidade)
    const isExcelConnected = false;

    // Obter token CSRF
    const { csrfToken } = useCSRF();

    const {
      messages,
      input,
      handleInputChange,
      handleSubmit: handleChatSubmit,
      setInput,
      isLoading: chatLoading,
    } = useChat({
      api: '/api/chat',
      body: { workbookId },
      headers: csrfToken ? { 'x-csrf-token': csrfToken } : {},
      onError: error => {
        handleError(error.message || t('requestError'));
        chatErrorHandler(error.message || t('requestError'));
      },
      onResponse: response => {
        clearError();

        if (response.status !== 200) {
          response
            .json()
            .then(data => {
              if (data.error) {
                handleError(data.error);
                chatErrorHandler(data.error);
              }
            })
            .catch(e => {
              console.error('Erro ao processar resposta:', e);
            });
        }
      },
      onFinish: async message => {
        // Tentar extrair metadados da mensagem final
        try {
          const extendedMessage = message as ExtendedAIMessage;

          // Verificar se a resposta contém metadados indicando operações executadas
          if (
            typeof extendedMessage === 'object' &&
            extendedMessage.operationsExecuted &&
            extendedMessage.dataUpdated
          ) {
            // Notificar componente pai para atualizar os dados
            if (onDataUpdated) {
              onDataUpdated();
            }

            // Mostrar toast de confirmação
            toast({
              title: t('messages.sheetUpdated'),
              description: t('messages.totalItems', {
                count: extendedMessage.operationCount || 0,
                item: extendedMessage.operationCount === 1 ? 'operation' : 'operations',
              }),
              variant: 'default',
            });
          }
        } catch (error) {
          console.error('Erro ao processar metadados da mensagem:', error);
        }
      },
    });

    // Definir handleSubmit antes de ser usado
    const handleSubmit = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (chatLoading || !input.trim()) return;

        clearError();
        handleChatSubmit(e);
      },
      [chatLoading, input, clearError, handleChatSubmit]
    );

    // Memoizar as funções que serão expostas através da ref
    const _setInputValue = useCallback(
      (value: string) => {
        setInput(value);
      },
      [setInput]
    );

    // Submeter mensagem via um método em ref
    const submitMessage = useCallback(
      (e?: React.FormEvent<HTMLFormElement>) => {
        if (e) e.preventDefault();
        if (chatLoading || !input.trim()) return;

        // Limpar todos os erros antes de submeter
        clearError();

        // Chamada da função para submeter mensagem
        handleChatSubmit(e || (new Event('submit') as unknown as React.FormEvent<HTMLFormElement>));
      },
      [handleChatSubmit, input, chatLoading, clearError]
    );

    // Expor métodos para o componente pai usando referências memoizadas
    useImperativeHandle(
      ref,
      () => ({
        setInputValue: (value: string) => setInput(value),
        submitMessage: () => submitMessage(),
      }),
      [setInput, submitMessage]
    );

    // Transformar as mensagens para incluir informações adicionais
    useEffect(() => {
      if (Array.isArray(messages)) {
        const messagesWithExtendedInfo = messages.map(msg => {
          return {
            ...msg,
            id: msg.id || `msg-${Date.now()}-${Math.random()}`,
          } as ExtendedAIMessage;
        });
        setExtendedMessages(messagesWithExtendedInfo);
      }
    }, [messages]);

    const messagesEndRef = useRef<HTMLDivElement>(null);

    // Efeito para rolagem - memoizar o callback de rolagem
    const scrollToBottom = useCallback(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        });
      }
    }, []);

    // Usar o callback memoizado no efeito
    useEffect(() => {
      scrollToBottom();
    }, [extendedMessages, scrollToBottom]);

    // Adicionar novas variáveis de estado para funcionalidades avançadas
    const [showSuggestions, _setShowSuggestions] = useState(true);
    const [showAutocomplete, setShowAutocomplete] = useState(false);
    const [autocompleteSelectedIndex, setAutocompleteSelectedIndex] = useState(0);
    const inputRef = useRef<HTMLInputElement>(null);
    const [isTyping, _setIsTyping] = useState(false);

    // Sugestões de comandos comuns - formato com categorias de cores e ícones
    const suggestions = [
      {
        text: 'Criar gráfico de barras',
        icon: <BarChart3 className="h-3 w-3" />,
        category: 'visualization',
      },
      {
        text: 'Calcular média',
        icon: <FileSpreadsheet className="h-3 w-3" />,
        category: 'analysis',
      },
      { text: 'Ordenar por', icon: <Search className="h-3 w-3" />, category: 'data' },
      {
        text: 'Formatar como moeda',
        icon: <Sparkles className="h-3 w-3" />,
        category: 'formatting',
      },
    ];

    // Opções de autocompletar baseadas no input atual
    const [autoCompleteOptions, setAutoCompleteOptions] = useState<
      Array<{ text: string; icon?: React.ReactNode }>
    >([]);

    // Função para lidar com cliques em sugestões
    const handleSuggestionClick = useCallback(
      (suggestion: { text: string; icon?: React.ReactNode }) => {
        setInput(suggestion.text);
        if (inputRef.current) {
          inputRef.current.focus();
        }
      },
      [setInput]
    );

    // Função para lidar com seleção de autocompletar
    const handleAutocompleteSelect = useCallback(
      (option?: { text: string; icon?: React.ReactNode }) => {
        if (option && option.text) {
          setInput(option.text);
          setShowAutocomplete(false);
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }
      },
      [setInput]
    );

    // Função para lidar com teclas especiais (setas, tab, etc.)
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (showAutocomplete && autoCompleteOptions.length > 0) {
          if (e.key === 'ArrowDown') {
            e.preventDefault();
            setAutocompleteSelectedIndex(prev =>
              prev < autoCompleteOptions.length - 1 ? prev + 1 : prev
            );
          } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            setAutocompleteSelectedIndex(prev => (prev > 0 ? prev - 1 : 0));
          } else if (e.key === 'Tab' || e.key === 'Enter') {
            if (showAutocomplete && autoCompleteOptions.length > 0) {
              e.preventDefault();
              const selectedOption = autoCompleteOptions[autocompleteSelectedIndex];
              if (selectedOption) {
                handleAutocompleteSelect(selectedOption);
              }
            }
          } else if (e.key === 'Escape') {
            setShowAutocomplete(false);
          }
        }
      },
      [showAutocomplete, autoCompleteOptions, autocompleteSelectedIndex, handleAutocompleteSelect]
    );

    // Monitorar mudanças no input para autocomplete
    useEffect(() => {
      if (input.length > 2) {
        // Aqui você implementaria a lógica real de autocomplete
        // Por exemplo, filtrar comandos baseados no que o usuário digitou
        const filteredOptions = [
          { text: `${input} na coluna A`, icon: <Search className="h-4 w-4" /> },
          { text: `${input} por região`, icon: <FileSpreadsheet className="h-4 w-4" /> },
          { text: `Visualizar ${input}`, icon: <BarChart3 className="h-4 w-4" /> },
        ];

        setAutoCompleteOptions(filteredOptions);
        setShowAutocomplete(filteredOptions.length > 0);
        setAutocompleteSelectedIndex(0);
      } else {
        setShowAutocomplete(false);
      }
    }, [input]);

    // Função para renderizar o cabeçalho do chat com status de conexão do Excel
    const renderChatHeader = () => (
      <div className="flex items-center justify-between p-3 border-b">
        <h3 className="text-sm font-medium">Assistente Excel</h3>
        {isExcelConnected && (
          <Badge variant="success" className="flex items-center gap-1.5 text-xs">
            <LinkIcon size={10} />
            <span>Excel Conectado</span>
          </Badge>
        )}
      </div>
    );

    return (
      <div className="flex flex-col h-full">
        {renderChatHeader()}

        <ScrollArea className="flex-1 px-3 pt-2">
          {extendedMessages.length === 0 ? (
            <EmptyState
              showSuggestions={showSuggestions}
              suggestions={suggestions}
              onSuggestionClick={handleSuggestionClick}
              isExcelConnected={isExcelConnected}
            />
          ) : (
            <div className="space-y-4 pt-4 pb-1">
              {extendedMessages.map((message, index) => (
                <MemoizedChatMessage
                  key={message.id || index}
                  message={message}
                  isLast={index === extendedMessages.length - 1}
                  isProcessing={
                    chatLoading &&
                    index === extendedMessages.length - 1 &&
                    message.role === 'assistant'
                  }
                  isExcelConnected={isExcelConnected}
                />
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        {/* Exibir mensagem de erro, se houver */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.3 }}
            >
              <ErrorMessage message={error} className={SPACING.margin.bottom.md} />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Área de input com recursos avançados */}
        <div
          className={cn(
            'relative',
            // Aplicar animação shake apenas quando houver erro
            error ? ANIMATIONS.shake : undefined
          )}
        >
          {/* Lista de sugestões/chips acima do input */}
          {showSuggestions && extendedMessages.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="flex flex-wrap gap-1.5 mb-2 px-1"
            >
              {suggestions.map((suggestion, index) => (
                <motion.button
                  key={index}
                  type="button"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="inline-flex items-center px-2.5 py-1 rounded-full text-xs bg-primary/10 text-primary hover:bg-primary/20 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  {suggestion.icon && <span className="mr-1">{suggestion.icon}</span>}
                  {suggestion.text}
                </motion.button>
              ))}
            </motion.div>
          )}

          <form onSubmit={handleSubmit} className="relative">
            <Input
              ref={inputRef}
              className={cn(
                'pr-10', // Espaço para o botão de envio
                BORDERS.radius.md,
                error ? 'border-destructive' : undefined
              )}
              variant={error ? 'error' : 'default'}
              placeholder={t(chatLoading ? 'chat.waitingResponse' : 'chat.placeholder')}
              value={input}
              onChange={handleInputChange}
              disabled={chatLoading}
              aria-label={t('chat.inputAriaLabel')}
              onKeyDown={handleKeyDown}
              autoComplete="off"
            />

            {/* Auto-complete dropdown */}
            {showAutocomplete && input.length > 0 && autoCompleteOptions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  'absolute bottom-full left-0 w-full bg-background border',
                  BORDERS.radius.md,
                  SHADOWS.md,
                  'mb-1 max-h-[200px] overflow-y-auto z-10'
                )}
              >
                {autoCompleteOptions.map((option, index) => (
                  <div
                    key={index}
                    className={cn(
                      SPACING.padding.sm,
                      'cursor-pointer text-sm hover:bg-muted flex items-center',
                      autocompleteSelectedIndex === index ? 'bg-muted' : ''
                    )}
                    onClick={() => handleAutocompleteSelect(option)}
                  >
                    {option.icon && (
                      <span className={cn('mr-2', COLORS.text.muted)}>{option.icon}</span>
                    )}
                    <span>{option.text}</span>
                  </div>
                ))}
              </motion.div>
            )}

            {/* Botão de envio */}
            <Button
              type="submit"
              size="icon"
              variant="ghost"
              className={cn(
                'absolute right-0 top-0 h-full aspect-square',
                'text-primary',
                // Desabilite com estilo quando estiver aguardando resposta
                chatLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-primary/10'
              )}
              disabled={chatLoading || !input.trim()}
              aria-label={t('chat.send')}
            >
              <Send className={SIZES.icon.sm} />
              <span className="sr-only">{t('chat.send')}</span>
            </Button>
          </form>

          {/* Indicador de digitação, se aplicável */}
          {isTyping && (
            <div
              className={cn(
                'absolute left-0 top-0 mt-[-24px]',
                TYPOGRAPHY.size.xs,
                COLORS.text.muted
              )}
            >
              {t('chat.typing')}
            </div>
          )}
        </div>
      </div>
    );
  }
);
