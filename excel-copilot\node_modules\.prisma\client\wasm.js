
Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  NotFoundError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime
} = require('@prisma/client/runtime/wasm.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.NotFoundError = NotFoundError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastIpAddress: 'lastIpAddress',
  lastLoginAt: 'lastLoginAt',
  loginCount: 'loginCount',
  userAgent: 'userAgent',
  isSuspicious: 'isSuspicious',
  isBanned: 'isBanned',
  banReason: 'banReason',
  banDate: 'banDate'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.WorkbookScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastAccessedAt: 'lastAccessedAt'
};

exports.Prisma.WorkbookShareScalarFieldEnum = {
  id: 'id',
  workbookId: 'workbookId',
  sharedByUserId: 'sharedByUserId',
  sharedWithUserId: 'sharedWithUserId',
  permissionLevel: 'permissionLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SheetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  workbookId: 'workbookId',
  data: 'data',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  message: 'message',
  response: 'response',
  workbookId: 'workbookId',
  createdAt: 'createdAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripePriceId: 'stripePriceId',
  status: 'status',
  plan: 'plan',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd',
  apiCallsLimit: 'apiCallsLimit',
  apiCallsUsed: 'apiCallsUsed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  currency: 'currency',
  stripePaymentId: 'stripePaymentId',
  stripeInvoiceId: 'stripeInvoiceId',
  status: 'status',
  subscriptionId: 'subscriptionId',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.ApiUsageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  count: 'count',
  endpoint: 'endpoint',
  workbookId: 'workbookId',
  billable: 'billable',
  createdAt: 'createdAt'
};

exports.Prisma.SecurityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  eventType: 'eventType',
  details: 'details',
  timestamp: 'timestamp'
};

exports.Prisma.UserActionLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  details: 'details',
  timestamp: 'timestamp'
};

exports.Prisma.CommandFeedbackScalarFieldEnum = {
  id: 'id',
  commandId: 'commandId',
  command: 'command',
  successful: 'successful',
  feedbackText: 'feedbackText',
  timestamp: 'timestamp'
};

exports.Prisma.AiMetricsScalarFieldEnum = {
  id: 'id',
  totalCommands: 'totalCommands',
  successfulCommands: 'successfulCommands',
  failedCommands: 'failedCommands',
  lastUpdated: 'lastUpdated'
};

exports.Prisma.WebVitalScalarFieldEnum = {
  id: 'id',
  name: 'name',
  value: 'value',
  rating: 'rating',
  delta: 'delta',
  metricId: 'metricId',
  navigationType: 'navigationType',
  url: 'url',
  userAgent: 'userAgent',
  timestamp: 'timestamp',
  createdAt: 'createdAt'
};

exports.Prisma.TemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  title: 'title',
  description: 'description',
  icon: 'icon',
  isPublic: 'isPublic',
  isActive: 'isActive',
  isFeatured: 'isFeatured',
  isNew: 'isNew',
  popularity: 'popularity',
  usageCount: 'usageCount',
  data: 'data',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateCategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  icon: 'icon',
  color: 'color',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateReviewScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  rating: 'rating',
  comment: 'comment',
  isHelpful: 'isHelpful',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateUsageScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  workbookId: 'workbookId',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Account: 'Account',
  Session: 'Session',
  User: 'User',
  VerificationToken: 'VerificationToken',
  Workbook: 'Workbook',
  WorkbookShare: 'WorkbookShare',
  Sheet: 'Sheet',
  ChatHistory: 'ChatHistory',
  Subscription: 'Subscription',
  Payment: 'Payment',
  ApiUsage: 'ApiUsage',
  SecurityLog: 'SecurityLog',
  UserActionLog: 'UserActionLog',
  CommandFeedback: 'CommandFeedback',
  AiMetrics: 'AiMetrics',
  WebVital: 'WebVital',
  Template: 'Template',
  TemplateCategory: 'TemplateCategory',
  TemplateReview: 'TemplateReview',
  TemplateUsage: 'TemplateUsage'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      },
      {
        "fromEnvVar": null,
        "value": "rhel-openssl-1.0.x"
      }
    ],
    "previewFeatures": [
      "driverAdapters",
      "metrics"
    ],
    "sourceFilePath": "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "5.22.0",
  "engineVersion": "605197351a3c8bdd595af2d2a9bc3025bca48ea2",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider        = \"prisma-client-js\"\n  previewFeatures = [\"metrics\", \"driverAdapters\"]\n  binaryTargets   = [\"native\", \"rhel-openssl-1.0.x\"]\n  engineType      = \"library\"\n}\n\ndatasource db {\n  provider     = \"postgresql\"\n  url          = env(\"DATABASE_URL\") // Conexão transação para aplicação (porta 6543)\n  directUrl    = env(\"DIRECT_URL\") // Conexão direta para migrations (porta 5432)\n  relationMode = \"prisma\"\n}\n\n// Modelos para autenticação\nmodel Account {\n  id                String  @id @default(cuid())\n  userId            String\n  type              String\n  provider          String\n  providerAccountId String\n  refresh_token     String? @db.Text\n  access_token      String? @db.Text\n  expires_at        Int?\n  token_type        String?\n  scope             String?\n  id_token          String? @db.Text\n  session_state     String?\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([provider, providerAccountId])\n  @@index([userId])\n  @@index([provider])\n}\n\nmodel Session {\n  id           String   @id @default(cuid())\n  sessionToken String   @unique\n  userId       String\n  expires      DateTime\n  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@index([userId])\n  @@index([expires])\n}\n\nmodel User {\n  id               String           @id @default(cuid())\n  name             String?\n  email            String?          @unique\n  emailVerified    DateTime?\n  image            String?\n  accounts         Account[]\n  sessions         Session[]\n  workbooks        Workbook[]\n  chatHistory      ChatHistory[]\n  subscriptions    Subscription[]\n  apiUsage         ApiUsage[]\n  createdAt        DateTime         @default(now())\n  updatedAt        DateTime         @updatedAt\n  // Relação para planilhas compartilhadas com o usuário\n  sharedWorkbooks  WorkbookShare[]  @relation(\"SharedWith\")\n  // Relação para planilhas que o usuário compartilhou\n  sharedByUser     WorkbookShare[]  @relation(\"SharedBy\")\n  lastIpAddress    String?\n  lastLoginAt      DateTime?\n  loginCount       Int              @default(0)\n  userAgent        String? // User agent do último login\n  isSuspicious     Boolean          @default(false) // Marcado para revisão manual\n  isBanned         Boolean          @default(false) // Banido do sistema\n  banReason        String? // Motivo do banimento\n  banDate          DateTime? // Data do banimento\n  securityLogs     SecurityLog[]\n  actionLogs       UserActionLog[]\n  // Relações com templates\n  createdTemplates Template[]\n  templateReviews  TemplateReview[]\n  templateUsage    TemplateUsage[]\n\n  @@index([email])\n  @@index([updatedAt])\n  @@index([isBanned]) // Índice para consultas rápidas de usuários banidos\n  @@index([isSuspicious]) // Índice para consultas de usuários suspeitos\n}\n\nmodel VerificationToken {\n  identifier String\n  token      String   @unique\n  expires    DateTime\n\n  @@unique([identifier, token])\n  @@index([expires])\n}\n\n// Modelos específicos da aplicação\nmodel Workbook {\n  id             String          @id @default(cuid())\n  name           String\n  description    String?         @db.Text\n  userId         String\n  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)\n  sheets         Sheet[]\n  isPublic       Boolean         @default(false)\n  createdAt      DateTime        @default(now())\n  updatedAt      DateTime        @updatedAt\n  lastAccessedAt DateTime        @default(now()) // Novo campo para rastrear último acesso\n  chatHistory    ChatHistory[]\n  // Relação para compartilhamentos desta planilha\n  shares         WorkbookShare[]\n  // Relação com templates (se foi criado a partir de um template)\n  templateUsage  TemplateUsage[]\n\n  @@index([userId])\n  @@index([isPublic])\n  @@index([updatedAt])\n  @@index([lastAccessedAt])\n  @@index([name])\n}\n\n// Novo modelo para compartilhamento de planilhas\nmodel WorkbookShare {\n  id               String   @id @default(cuid())\n  workbookId       String\n  workbook         Workbook @relation(fields: [workbookId], references: [id], onDelete: Cascade)\n  sharedByUserId   String\n  sharedByUser     User     @relation(\"SharedBy\", fields: [sharedByUserId], references: [id], onDelete: Cascade)\n  sharedWithUserId String\n  sharedWithUser   User     @relation(\"SharedWith\", fields: [sharedWithUserId], references: [id], onDelete: Cascade)\n  permissionLevel  String   @default(\"READ\") // READ, EDIT, ADMIN\n  createdAt        DateTime @default(now())\n  updatedAt        DateTime @updatedAt\n\n  @@unique([workbookId, sharedWithUserId])\n  @@index([workbookId])\n  @@index([sharedByUserId])\n  @@index([sharedWithUserId])\n  @@index([permissionLevel])\n}\n\nmodel Sheet {\n  id         String   @id @default(cuid())\n  name       String\n  workbookId String\n  workbook   Workbook @relation(fields: [workbookId], references: [id], onDelete: Cascade)\n  data       String?  @db.Text // Armazena os dados da planilha em formato JSON (serializado)\n  createdAt  DateTime @default(now())\n  updatedAt  DateTime @updatedAt\n\n  @@index([workbookId])\n  @@index([updatedAt])\n  @@index([name])\n}\n\nmodel ChatHistory {\n  id         String    @id @default(cuid())\n  userId     String\n  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  message    String    @db.Text\n  response   String    @db.Text\n  workbookId String?\n  workbook   Workbook? @relation(fields: [workbookId], references: [id], onDelete: SetNull)\n  createdAt  DateTime  @default(now())\n\n  @@index([userId])\n  @@index([workbookId])\n  @@index([createdAt])\n}\n\n// Modelos de pagamento e assinatura\nmodel Subscription {\n  id                   String    @id @default(cuid())\n  userId               String\n  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  stripeCustomerId     String?\n  stripeSubscriptionId String?   @unique\n  stripePriceId        String?\n  status               String    @default(\"active\")\n  plan                 String\n  cancelAtPeriodEnd    Boolean   @default(false)\n  currentPeriodStart   DateTime?\n  currentPeriodEnd     DateTime?\n  apiCallsLimit        Int       @default(50)\n  apiCallsUsed         Int       @default(0)\n  payments             Payment[]\n  createdAt            DateTime  @default(now())\n  updatedAt            DateTime  @updatedAt\n\n  @@index([userId])\n  @@index([status])\n  @@index([plan])\n  @@index([createdAt])\n  @@index([userId, currentPeriodEnd])\n}\n\nmodel Payment {\n  id              String        @id @default(cuid())\n  amount          Int // em centavos\n  currency        String        @default(\"BRL\")\n  stripePaymentId String?\n  stripeInvoiceId String?\n  status          String // succeeded, pending, failed\n  subscriptionId  String?\n  subscription    Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)\n  metadata        String?       @db.Text // JSON com informações adicionais\n  createdAt       DateTime      @default(now())\n\n  @@index([subscriptionId])\n  @@index([status])\n  @@index([stripePaymentId])\n}\n\nmodel ApiUsage {\n  id         String   @id @default(cuid())\n  userId     String\n  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  count      Int      @default(1)\n  endpoint   String // qual endpoint foi usado (chat, análise, etc)\n  workbookId String?\n  billable   Boolean  @default(true)\n  createdAt  DateTime @default(now())\n\n  @@index([userId])\n  @@index([createdAt])\n  @@index([endpoint])\n  @@index([workbookId])\n  @@index([userId, createdAt])\n  @@index([endpoint, createdAt])\n}\n\n// Tabela para registro de eventos de segurança (tentativas de burlar limites, etc)\nmodel SecurityLog {\n  id        String   @id @default(cuid())\n  userId    String\n  eventType String\n  details   String?  @db.Text\n  timestamp DateTime @default(now())\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@index([userId])\n  @@index([eventType])\n  @@index([timestamp])\n}\n\n// Tabela para registro de ações do usuário (para análise de padrões)\nmodel UserActionLog {\n  id        String   @id @default(cuid())\n  userId    String\n  action    String\n  details   String?  @db.Text\n  timestamp DateTime @default(now())\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@index([userId])\n  @@index([action])\n  @@index([timestamp])\n}\n\n// Modelo para armazenar feedback de comandos\nmodel CommandFeedback {\n  id           String   @id @default(cuid())\n  commandId    String\n  command      String   @db.Text\n  successful   Boolean\n  feedbackText String?  @db.Text\n  timestamp    DateTime @default(now())\n\n  @@index([commandId])\n  @@index([successful])\n  @@index([timestamp])\n}\n\n// Modelo para métricas agregadas de IA\nmodel AiMetrics {\n  id                 String   @id @default(cuid())\n  totalCommands      Int      @default(0)\n  successfulCommands Int      @default(0)\n  failedCommands     Int      @default(0)\n  lastUpdated        DateTime @default(now()) @updatedAt\n}\n\n// Modelo para métricas de Web Vitals\nmodel WebVital {\n  id             String   @id @default(cuid())\n  name           String // LCP, FID, CLS, FCP, TTFB\n  value          Float // Valor da métrica\n  rating         String // good, needs-improvement, poor\n  delta          Float // Delta da métrica\n  metricId       String // ID único da métrica\n  navigationType String // Tipo de navegação\n  url            String   @db.Text // URL onde a métrica foi coletada\n  userAgent      String   @db.Text // User agent do navegador\n  timestamp      DateTime // Timestamp da coleta\n  createdAt      DateTime @default(now())\n\n  @@index([name])\n  @@index([rating])\n  @@index([timestamp])\n  @@index([url])\n  @@index([name, timestamp])\n  @@index([rating, timestamp])\n}\n\n// Modelos para sistema de templates\nmodel Template {\n  id          String   @id @default(cuid())\n  name        String\n  title       String\n  description String?  @db.Text\n  icon        String? // Nome do ícone (ex: 'bar-chart', 'piggy-bank')\n  isPublic    Boolean  @default(true)\n  isActive    Boolean  @default(true)\n  isFeatured  Boolean  @default(false)\n  isNew       Boolean  @default(false)\n  popularity  Int      @default(0)\n  usageCount  Int      @default(0)\n  data        String   @db.Text // JSON com estrutura das sheets\n  createdBy   String? // ID do usuário criador (null para templates do sistema)\n  creator     User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n\n  // Relacionamentos\n  categories TemplateCategory[]\n  reviews    TemplateReview[]\n  usage      TemplateUsage[]\n\n  @@index([isPublic])\n  @@index([isActive])\n  @@index([isFeatured])\n  @@index([popularity])\n  @@index([createdBy])\n  @@index([createdAt])\n}\n\nmodel TemplateCategory {\n  id          String   @id @default(cuid())\n  name        String   @unique\n  slug        String   @unique\n  description String?\n  icon        String?\n  color       String? // Cor para UI (ex: '#3B82F6')\n  isActive    Boolean  @default(true)\n  sortOrder   Int      @default(0)\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n\n  // Relacionamentos\n  templates Template[]\n\n  @@index([isActive])\n  @@index([sortOrder])\n}\n\nmodel TemplateReview {\n  id         String   @id @default(cuid())\n  templateId String\n  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade)\n  userId     String\n  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  rating     Int // 1-5 estrelas\n  comment    String?  @db.Text\n  isHelpful  Boolean  @default(false)\n  createdAt  DateTime @default(now())\n  updatedAt  DateTime @updatedAt\n\n  @@unique([templateId, userId]) // Um usuário só pode avaliar um template uma vez\n  @@index([templateId])\n  @@index([userId])\n  @@index([rating])\n  @@index([createdAt])\n}\n\nmodel TemplateUsage {\n  id         String    @id @default(cuid())\n  templateId String\n  template   Template  @relation(fields: [templateId], references: [id], onDelete: Cascade)\n  userId     String\n  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  workbookId String? // ID do workbook criado (se ainda existir)\n  workbook   Workbook? @relation(fields: [workbookId], references: [id], onDelete: SetNull)\n  createdAt  DateTime  @default(now())\n\n  @@index([templateId])\n  @@index([userId])\n  @@index([workbookId])\n  @@index([createdAt])\n}\n",
  "inlineSchemaHash": "a35c1f14c19c039dddaed57868ef116ec94ccefa9d1d605ecbb713879799b230",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"Account\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"provider\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"providerAccountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"refresh_token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"access_token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expires_at\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"token_type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"scope\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"id_token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"session_state\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"AccountToUser\"}],\"dbName\":null},\"Session\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"sessionToken\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expires\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SessionToUser\"}],\"dbName\":null},\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"emailVerified\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"image\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"accounts\",\"kind\":\"object\",\"type\":\"Account\",\"relationName\":\"AccountToUser\"},{\"name\":\"sessions\",\"kind\":\"object\",\"type\":\"Session\",\"relationName\":\"SessionToUser\"},{\"name\":\"workbooks\",\"kind\":\"object\",\"type\":\"Workbook\",\"relationName\":\"UserToWorkbook\"},{\"name\":\"chatHistory\",\"kind\":\"object\",\"type\":\"ChatHistory\",\"relationName\":\"ChatHistoryToUser\"},{\"name\":\"subscriptions\",\"kind\":\"object\",\"type\":\"Subscription\",\"relationName\":\"SubscriptionToUser\"},{\"name\":\"apiUsage\",\"kind\":\"object\",\"type\":\"ApiUsage\",\"relationName\":\"ApiUsageToUser\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"sharedWorkbooks\",\"kind\":\"object\",\"type\":\"WorkbookShare\",\"relationName\":\"SharedWith\"},{\"name\":\"sharedByUser\",\"kind\":\"object\",\"type\":\"WorkbookShare\",\"relationName\":\"SharedBy\"},{\"name\":\"lastIpAddress\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"lastLoginAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"loginCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isSuspicious\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"isBanned\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"banReason\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"banDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"securityLogs\",\"kind\":\"object\",\"type\":\"SecurityLog\",\"relationName\":\"SecurityLogToUser\"},{\"name\":\"actionLogs\",\"kind\":\"object\",\"type\":\"UserActionLog\",\"relationName\":\"UserToUserActionLog\"},{\"name\":\"createdTemplates\",\"kind\":\"object\",\"type\":\"Template\",\"relationName\":\"TemplateToUser\"},{\"name\":\"templateReviews\",\"kind\":\"object\",\"type\":\"TemplateReview\",\"relationName\":\"TemplateReviewToUser\"},{\"name\":\"templateUsage\",\"kind\":\"object\",\"type\":\"TemplateUsage\",\"relationName\":\"TemplateUsageToUser\"}],\"dbName\":null},\"VerificationToken\":{\"fields\":[{\"name\":\"identifier\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expires\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Workbook\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToWorkbook\"},{\"name\":\"sheets\",\"kind\":\"object\",\"type\":\"Sheet\",\"relationName\":\"SheetToWorkbook\"},{\"name\":\"isPublic\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastAccessedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"chatHistory\",\"kind\":\"object\",\"type\":\"ChatHistory\",\"relationName\":\"ChatHistoryToWorkbook\"},{\"name\":\"shares\",\"kind\":\"object\",\"type\":\"WorkbookShare\",\"relationName\":\"WorkbookToWorkbookShare\"},{\"name\":\"templateUsage\",\"kind\":\"object\",\"type\":\"TemplateUsage\",\"relationName\":\"TemplateUsageToWorkbook\"}],\"dbName\":null},\"WorkbookShare\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbookId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbook\",\"kind\":\"object\",\"type\":\"Workbook\",\"relationName\":\"WorkbookToWorkbookShare\"},{\"name\":\"sharedByUserId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"sharedByUser\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SharedBy\"},{\"name\":\"sharedWithUserId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"sharedWithUser\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SharedWith\"},{\"name\":\"permissionLevel\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Sheet\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbookId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbook\",\"kind\":\"object\",\"type\":\"Workbook\",\"relationName\":\"SheetToWorkbook\"},{\"name\":\"data\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"ChatHistory\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ChatHistoryToUser\"},{\"name\":\"message\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"response\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbookId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbook\",\"kind\":\"object\",\"type\":\"Workbook\",\"relationName\":\"ChatHistoryToWorkbook\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Subscription\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SubscriptionToUser\"},{\"name\":\"stripeCustomerId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripeSubscriptionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripePriceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"plan\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"cancelAtPeriodEnd\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"currentPeriodStart\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"currentPeriodEnd\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"apiCallsLimit\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"apiCallsUsed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"payments\",\"kind\":\"object\",\"type\":\"Payment\",\"relationName\":\"PaymentToSubscription\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Payment\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"currency\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripePaymentId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"stripeInvoiceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"subscriptionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"subscription\",\"kind\":\"object\",\"type\":\"Subscription\",\"relationName\":\"PaymentToSubscription\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"ApiUsage\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ApiUsageToUser\"},{\"name\":\"count\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"endpoint\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbookId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"billable\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"SecurityLog\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"eventType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"details\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SecurityLogToUser\"}],\"dbName\":null},\"UserActionLog\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"action\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"details\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserActionLog\"}],\"dbName\":null},\"CommandFeedback\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"commandId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"command\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"successful\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"feedbackText\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"AiMetrics\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"totalCommands\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"successfulCommands\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"failedCommands\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lastUpdated\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"WebVital\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"rating\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"delta\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"metricId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"navigationType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"url\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Template\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"icon\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isPublic\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"isFeatured\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"isNew\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"popularity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"usageCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"data\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"creator\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TemplateToUser\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"categories\",\"kind\":\"object\",\"type\":\"TemplateCategory\",\"relationName\":\"TemplateToTemplateCategory\"},{\"name\":\"reviews\",\"kind\":\"object\",\"type\":\"TemplateReview\",\"relationName\":\"TemplateToTemplateReview\"},{\"name\":\"usage\",\"kind\":\"object\",\"type\":\"TemplateUsage\",\"relationName\":\"TemplateToTemplateUsage\"}],\"dbName\":null},\"TemplateCategory\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"slug\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"icon\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"color\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"sortOrder\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"templates\",\"kind\":\"object\",\"type\":\"Template\",\"relationName\":\"TemplateToTemplateCategory\"}],\"dbName\":null},\"TemplateReview\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"templateId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"template\",\"kind\":\"object\",\"type\":\"Template\",\"relationName\":\"TemplateToTemplateReview\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TemplateReviewToUser\"},{\"name\":\"rating\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"comment\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isHelpful\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"TemplateUsage\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"templateId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"template\",\"kind\":\"object\",\"type\":\"Template\",\"relationName\":\"TemplateToTemplateUsage\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TemplateUsageToUser\"},{\"name\":\"workbookId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workbook\",\"kind\":\"object\",\"type\":\"Workbook\",\"relationName\":\"TemplateUsageToWorkbook\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine 
  }
}

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

