import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/server/auth/options';
import { prisma } from '@/server/db/client';
import { templateFilterSchema, createTemplateSchema } from '@/schemas/template';
import { ApiResponse } from '@/lib/api-response';

/**
 * GET /api/templates
 * Lista templates disponíveis com filtros opcionais
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Validar parâmetros de filtro
    const filterParams = {
      categoryId: searchParams.get('categoryId') || undefined,
      isPublic: searchParams.get('isPublic') ? searchParams.get('isPublic') === 'true' : undefined,
      isFeatured: searchParams.get('isFeatured') ? searchParams.get('isFeatured') === 'true' : undefined,
      isNew: searchParams.get('isNew') ? searchParams.get('isNew') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') || 'popularity',
      sortOrder: searchParams.get('sortOrder') || 'desc',
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
    };

    const filters = templateFilterSchema.parse(filterParams);

    // Construir query de busca
    const where: any = {
      isActive: true,
    };

    if (filters.categoryId) {
      where.categories = {
        some: {
          id: filters.categoryId,
        },
      };
    }

    if (filters.isPublic !== undefined) {
      where.isPublic = filters.isPublic;
    }

    if (filters.isFeatured !== undefined) {
      where.isFeatured = filters.isFeatured;
    }

    if (filters.isNew !== undefined) {
      where.isNew = filters.isNew;
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    // Definir ordenação
    const orderBy: any = {};
    switch (filters.sortBy) {
      case 'popularity':
        orderBy.popularity = filters.sortOrder;
        break;
      case 'recent':
        orderBy.createdAt = filters.sortOrder;
        break;
      case 'name':
        orderBy.name = filters.sortOrder;
        break;
      case 'usage':
        orderBy.usageCount = filters.sortOrder;
        break;
      default:
        orderBy.popularity = 'desc';
    }

    // Buscar templates
    const [templates, total] = await Promise.all([
      prisma.template.findMany({
        where,
        orderBy,
        take: filters.limit,
        skip: filters.offset,
        include: {
          categories: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          _count: {
            select: {
              reviews: true,
              usage: true,
            },
          },
        },
      }),
      prisma.template.count({ where }),
    ]);

    // Calcular média de avaliações para cada template
    const templatesWithRatings = await Promise.all(
      templates.map(async (template) => {
        const avgRating = await prisma.templateReview.aggregate({
          where: { templateId: template.id },
          _avg: { rating: true },
        });

        return {
          id: template.id,
          name: template.name,
          title: template.title,
          description: template.description,
          icon: template.icon,
          isPublic: template.isPublic,
          isFeatured: template.isFeatured,
          isNew: template.isNew,
          popularity: template.popularity,
          usageCount: template.usageCount,
          categories: template.categories,
          creator: template.creator,
          reviewCount: template._count.reviews,
          usageTotal: template._count.usage,
          averageRating: avgRating._avg.rating || 0,
          createdAt: template.createdAt,
          updatedAt: template.updatedAt,
        };
      })
    );

    return ApiResponse.success({
      templates: templatesWithRatings,
      pagination: {
        total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: filters.offset + filters.limit < total,
      },
    });
  } catch (error) {
    console.error('Erro ao buscar templates:', error);
    return ApiResponse.error(
      error instanceof Error ? error.message : 'Erro interno do servidor',
      500
    );
  }
}

/**
 * POST /api/templates
 * Cria um novo template (requer autenticação)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return ApiResponse.error('Não autorizado', 401);
    }

    const body = await request.json();
    const templateData = createTemplateSchema.parse(body);

    // Criar template
    const template = await prisma.template.create({
      data: {
        name: templateData.name,
        title: templateData.title,
        description: templateData.description || null,
        icon: templateData.icon || null,
        isPublic: templateData.isPublic,
        isFeatured: templateData.isFeatured,
        isNew: templateData.isNew,
        data: JSON.stringify(templateData.data),
        createdBy: session.user.id,
        // Conectar categorias se fornecidas
        ...(templateData.categoryIds && templateData.categoryIds.length > 0 && {
          categories: {
            connect: templateData.categoryIds.map((id) => ({ id })),
          },
        }),
      },
      include: {
        categories: true,
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return ApiResponse.success(template, 201);
  } catch (error) {
    console.error('Erro ao criar template:', error);
    return ApiResponse.error(
      error instanceof Error ? error.message : 'Erro interno do servidor',
      500
    );
  }
}
