import type { HandlerDataConsole } from '../../types-hoist/instrument';
/**
 * Add an instrumentation handler for when a console.xxx method is called.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addConsoleInstrumentationHandler(handler: (data: HandlerDataConsole) => void): void;
//# sourceMappingURL=console.d.ts.map