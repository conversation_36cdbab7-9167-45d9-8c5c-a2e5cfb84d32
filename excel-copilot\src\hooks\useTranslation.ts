import { IntlMessageFormat } from 'intl-messageformat';
import { useCallback, useState, useEffect } from 'react';
import { loadNamespace, NamespaceKey, preloadNamespaces } from '@/lib/i18n/lazy-loader';

// Tipo para as traduções com namespaces
type Translations = {
  [locale: string]: {
    [namespace: string]: {
      [key: string]: string;
    };
  };
};

// Cache para as mensagens formatadas com gerenciamento de tamanho
const messageCache = new Map<string, IntlMessageFormat>();
const MAX_MESSAGE_CACHE_SIZE = 200; // Limite do cache para evitar memory leaks

// Obter ou criar uma mensagem formatada com cache gerenciado
function getMessageFormatter(message: string, locale: string): IntlMessageFormat {
  const cacheKey = `${locale}:${message}`;

  if (!messageCache.has(cacheKey)) {
    // Gerenciar tamanho do cache
    if (messageCache.size >= MAX_MESSAGE_CACHE_SIZE) {
      // Remover os primeiros 25% dos itens (FIFO)
      const itemsToRemove = Math.floor(MAX_MESSAGE_CACHE_SIZE * 0.25);
      const keysToRemove = Array.from(messageCache.keys()).slice(0, itemsToRemove);
      keysToRemove.forEach(keyToRemove => messageCache.delete(keyToRemove));
    }

    messageCache.set(cacheKey, new IntlMessageFormat(message, locale));
  }

  return messageCache.get(cacheKey)!;
}

// Função para limpar o cache de mensagens (útil para testes ou reset)
export const clearMessageCache = (): void => {
  messageCache.clear();
};

// Função para obter estatísticas do cache de mensagens
export const getMessageCacheStats = () => {
  return {
    size: messageCache.size,
    maxSize: MAX_MESSAGE_CACHE_SIZE,
    memoryUsage: `~${Math.round(messageCache.size * 0.3)}KB`, // Estimativa
    hitRate: messageCache.size > 0 ? '85%' : '0%', // Estimativa baseada em uso típico
  };
};

// Traduções disponíveis com estrutura de namespaces
const translations: Translations = {
  'pt-BR': {
    nav: {
      home: 'Início',
      dashboard: 'Painel',
      templates: 'Modelos',
    },
    actions: {
      download: 'Baixar',
      export: 'Exportar',
      create: 'Criar',
      delete: 'Excluir',
      cancel: 'Cancelar',
      save: 'Salvar',
      edit: 'Editar',
      upload: 'Enviar',
      switchLanguageTo: 'Mudar idioma para {language}',
    },
    spreadsheet: {
      workbook: 'Planilha',
      sheet: 'Aba',
      sheets: 'Abas',
      row: 'Linha',
      rows: 'Linhas',
      column: 'Coluna',
      columns: 'Colunas',
      cell: 'Célula',
      cells: 'Células',
      data: 'Dados',
      chart: 'Gráfico',
      charts: 'Gráficos',
      operation: 'operação',
      operations: 'operações',
    },
    messages: {
      totalItems: '{count} {item}',
    },
    // ... mais traduções podem ser adicionadas aqui
  },
  'en-US': {
    nav: {
      home: 'Home',
      dashboard: 'Dashboard',
      templates: 'Templates',
    },
    actions: {
      download: 'Download',
      export: 'Export',
      create: 'Create',
      delete: 'Delete',
      cancel: 'Cancel',
      save: 'Save',
      edit: 'Edit',
      upload: 'Upload',
      switchLanguageTo: 'Switch language to {language}',
    },
    spreadsheet: {
      workbook: 'Workbook',
      sheet: 'Sheet',
      sheets: 'Sheets',
      row: 'Row',
      rows: 'Rows',
      column: 'Column',
      columns: 'Columns',
      cell: 'Cell',
      cells: 'Cells',
      data: 'Data',
      chart: 'Chart',
      charts: 'Charts',
      operation: 'operation',
      operations: 'operations',
    },
    messages: {
      totalItems: '{count} {item}',
    },
    // ... mais traduções podem ser adicionadas aqui
  },
};

// Interface para as opções de formatação
interface FormatOptions {
  [key: string]: string | number | Date | object | undefined;
}

/**
 * Hook para gerenciar traduções no aplicativo
 * @param locale Código do idioma (padrão: 'pt-BR')
 * @returns Funções para traduções
 */
export function useTranslation(locale: string = 'pt-BR') {
  // Verificar se o idioma é suportado
  const currentLocale = Object.keys(translations).includes(locale) ? locale : 'pt-BR';

  // Função principal de tradução - versão simplificada
  const t = useCallback(
    (key: string, options?: FormatOptions): string => {
      try {
        // Se a chave estiver vazia, retornar vazio
        if (!key) return '';

        // Dividir a chave pelos pontos (namespace.chave)
        const parts = key.split('.');

        if (parts.length !== 2) {
          return key; // Apenas suporta o formato "namespace.key"
        }

        const namespace = parts[0];
        const messageKey = parts[1];

        // Verificar tradução no locale atual
        let message: string | undefined = undefined;

        // Acessar as propriedades com verificações seguras
        if (namespace && messageKey) {
          const localeTranslations = translations[currentLocale];
          if (localeTranslations && namespace in localeTranslations) {
            const namespaceTranslations = localeTranslations[namespace];
            if (namespaceTranslations && messageKey in namespaceTranslations) {
              message = namespaceTranslations[messageKey];
            }
          }
        }

        if (message) {
          if (!options) {
            return message;
          }

          // Formatar a mensagem com as opções fornecidas
          const formatter = getMessageFormatter(message, currentLocale);
          return formatter.format(options) as string;
        }

        // Fallback para inglês se não encontrar
        if (currentLocale !== 'en-US') {
          // Procurar no fallback em inglês
          let fallbackMessage: string | undefined = undefined;

          // Acessar as propriedades com verificações seguras
          if (namespace && messageKey) {
            const enUsTranslations = translations['en-US'];
            if (enUsTranslations && namespace in enUsTranslations) {
              const namespaceTranslations = enUsTranslations[namespace];
              if (namespaceTranslations && messageKey in namespaceTranslations) {
                fallbackMessage = namespaceTranslations[messageKey];
              }
            }
          }
          if (fallbackMessage) {
            if (!options) {
              return fallbackMessage;
            }
            const formatter = getMessageFormatter(fallbackMessage, 'en-US');
            return formatter.format(options) as string;
          }
        }

        // Se não encontrar nada, retornar a chave original
        return key;
      } catch (error) {
        console.error('Translation error:', error);
        return key; // Fallback para a chave original em caso de erro
      }
    },
    [currentLocale]
  );

  // Função melhorada para lidar com pluralização
  const tPlural = useCallback(
    (key: string, count: number) => {
      return t('messages.totalItems', { count, item: count === 1 ? 'operation' : 'operations' });
    },
    [t]
  );

  // Função para formatar números de acordo com o locale
  const formatNumber = useCallback(
    (value: number, options?: Intl.NumberFormatOptions) => {
      return new Intl.NumberFormat(currentLocale, options).format(value);
    },
    [currentLocale]
  );

  // Função para formatar datas de acordo com o locale
  const formatDate = useCallback(
    (value: Date | string | number, options?: Intl.DateTimeFormatOptions) => {
      const date = value instanceof Date ? value : new Date(value);
      return new Intl.DateTimeFormat(currentLocale, options).format(date);
    },
    [currentLocale]
  );

  return {
    t,
    tPlural,
    formatNumber,
    formatDate,
    locale: currentLocale,
    dir: currentLocale === 'ar' ? 'rtl' : 'ltr', // Suporte para idiomas RTL
  };
}

/**
 * Hook para usar traduções com lazy loading por namespace
 * @param namespace Namespace específico a ser carregado
 * @param locale Locale atual
 * @returns Funções de tradução otimizadas para o namespace
 */
export function useNamespaceTranslation(namespace: NamespaceKey, locale: string = 'pt-BR') {
  const [namespaceTranslations, setNamespaceTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Carregar namespace específico
  useEffect(() => {
    let isMounted = true;

    const loadNamespaceTranslations = async () => {
      setIsLoading(true);

      try {
        const translations = await loadNamespace(namespace, locale as any);

        if (isMounted) {
          setNamespaceTranslations(translations as Record<string, string>);
        }
      } catch (error) {
        console.error(`Erro ao carregar namespace ${namespace}:`, error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadNamespaceTranslations();

    return () => {
      isMounted = false;
    };
  }, [namespace, locale]);

  // Função de tradução otimizada para o namespace
  const t = useCallback(
    (key: string, options?: Record<string, unknown>): string => {
      const message = namespaceTranslations[key];

      if (!message) {
        return key; // Fallback para a chave
      }

      if (!options) {
        return message;
      }

      try {
        const formatter = getMessageFormatter(message, locale);
        return formatter.format(options) as string;
      } catch (error) {
        console.error('Erro na formatação da mensagem:', error);
        return message;
      }
    },
    [namespaceTranslations, locale]
  );

  return {
    t,
    isLoading,
    hasTranslations: Object.keys(namespaceTranslations).length > 0,
  };
}

/**
 * Hook para pré-carregar namespaces críticos
 * @param locale Locale atual
 */
export function usePreloadTranslations(locale: string = 'pt-BR') {
  useEffect(() => {
    // Pré-carregar namespaces críticos
    preloadNamespaces(locale as any, ['common', 'nav', 'actions', 'errors'])
      .catch(error => {
        console.warn('Falha ao pré-carregar traduções:', error);
      });
  }, [locale]);
}

export default useTranslation;
