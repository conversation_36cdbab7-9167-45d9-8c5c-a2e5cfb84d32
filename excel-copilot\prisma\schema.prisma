// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics", "driverAdapters"]
  binaryTargets   = ["native", "rhel-openssl-1.0.x"]
  engineType      = "library"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")      // Conexão transação para aplicação (porta 6543)
  directUrl    = env("DIRECT_URL")        // Conexão direta para migrations (porta 5432)
  relationMode = "prisma"
}

// Modelos para autenticação
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@index([provider])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expires])
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  workbooks     Workbook[]
  chatHistory   ChatHistory[]
  subscriptions Subscription[]
  apiUsage      ApiUsage[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  // Relação para planilhas compartilhadas com o usuário
  sharedWorkbooks WorkbookShare[] @relation("SharedWith")
  // Relação para planilhas que o usuário compartilhou
  sharedByUser    WorkbookShare[] @relation("SharedBy")
  lastIpAddress   String?
  lastLoginAt     DateTime?
  loginCount      Int       @default(0)
  userAgent       String?   // User agent do último login
  isSuspicious    Boolean   @default(false) // Marcado para revisão manual
  isBanned        Boolean   @default(false) // Banido do sistema
  banReason       String?   // Motivo do banimento
  banDate         DateTime? // Data do banimento
  securityLogs    SecurityLog[]
  actionLogs      UserActionLog[]
  // Relações com templates
  createdTemplates Template[]
  templateReviews  TemplateReview[]
  templateUsage    TemplateUsage[]

  @@index([email])
  @@index([updatedAt])
  @@index([isBanned]) // Índice para consultas rápidas de usuários banidos
  @@index([isSuspicious]) // Índice para consultas de usuários suspeitos
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@index([expires])
}

// Modelos específicos da aplicação
model Workbook {
  id            String   @id @default(cuid())
  name          String
  description   String?  @db.Text
  userId        String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  sheets        Sheet[]
  isPublic      Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  lastAccessedAt DateTime @default(now()) // Novo campo para rastrear último acesso
  chatHistory   ChatHistory[]
  // Relação para compartilhamentos desta planilha
  shares        WorkbookShare[]
  // Relação com templates (se foi criado a partir de um template)
  templateUsage TemplateUsage[]

  @@index([userId])
  @@index([isPublic])
  @@index([updatedAt])
  @@index([lastAccessedAt])
  @@index([name])
}

// Novo modelo para compartilhamento de planilhas
model WorkbookShare {
  id                String   @id @default(cuid())
  workbookId        String
  workbook          Workbook @relation(fields: [workbookId], references: [id], onDelete: Cascade)
  sharedByUserId    String
  sharedByUser      User     @relation("SharedBy", fields: [sharedByUserId], references: [id], onDelete: Cascade)
  sharedWithUserId  String
  sharedWithUser    User     @relation("SharedWith", fields: [sharedWithUserId], references: [id], onDelete: Cascade)
  permissionLevel   String   @default("READ") // READ, EDIT, ADMIN
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([workbookId, sharedWithUserId])
  @@index([workbookId])
  @@index([sharedByUserId])
  @@index([sharedWithUserId])
  @@index([permissionLevel])
}

model Sheet {
  id         String   @id @default(cuid())
  name       String
  workbookId String
  workbook   Workbook @relation(fields: [workbookId], references: [id], onDelete: Cascade)
  data       String?  @db.Text // Armazena os dados da planilha em formato JSON (serializado)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([workbookId])
  @@index([updatedAt])
  @@index([name])
}

model ChatHistory {
  id         String   @id @default(cuid())
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  message    String   @db.Text
  response   String   @db.Text
  workbookId String?
  workbook   Workbook? @relation(fields: [workbookId], references: [id], onDelete: SetNull)
  createdAt  DateTime @default(now())

  @@index([userId])
  @@index([workbookId])
  @@index([createdAt])
}

// Modelos de pagamento e assinatura
model Subscription {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stripeCustomerId  String?
  stripeSubscriptionId String?  @unique
  stripePriceId     String?
  status            String   @default("active")
  plan              String
  cancelAtPeriodEnd Boolean  @default(false)
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  apiCallsLimit     Int      @default(50)
  apiCallsUsed      Int      @default(0)
  payments          Payment[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId])
  @@index([status])
  @@index([plan])
  @@index([createdAt])
  @@index([userId, currentPeriodEnd])
}

model Payment {
  id                String   @id @default(cuid())
  amount            Int      // em centavos
  currency          String   @default("BRL")
  stripePaymentId   String?
  stripeInvoiceId   String?
  status            String   // succeeded, pending, failed
  subscriptionId    String?
  subscription      Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)
  metadata          String?  @db.Text // JSON com informações adicionais
  createdAt         DateTime @default(now())

  @@index([subscriptionId])
  @@index([status])
  @@index([stripePaymentId])
}

model ApiUsage {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  count       Int      @default(1)
  endpoint    String   // qual endpoint foi usado (chat, análise, etc)
  workbookId  String?
  billable    Boolean  @default(true)
  createdAt   DateTime @default(now())

  @@index([userId])
  @@index([createdAt])
  @@index([endpoint])
  @@index([workbookId])
  @@index([userId, createdAt])
  @@index([endpoint, createdAt])
}

// Tabela para registro de eventos de segurança (tentativas de burlar limites, etc)
model SecurityLog {
  id        String   @id @default(cuid())
  userId    String
  eventType String
  details   String?  @db.Text
  timestamp DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
}

// Tabela para registro de ações do usuário (para análise de padrões)
model UserActionLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?  @db.Text
  timestamp DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([action])
  @@index([timestamp])
}

// Modelo para armazenar feedback de comandos
model CommandFeedback {
  id           String   @id @default(cuid())
  commandId    String
  command      String   @db.Text
  successful   Boolean
  feedbackText String?  @db.Text
  timestamp    DateTime @default(now())

  @@index([commandId])
  @@index([successful])
  @@index([timestamp])
}

// Modelo para métricas agregadas de IA
model AiMetrics {
  id                String   @id @default(cuid())
  totalCommands     Int      @default(0)
  successfulCommands Int     @default(0)
  failedCommands    Int      @default(0)
  lastUpdated       DateTime @default(now()) @updatedAt
}

// Modelo para métricas de Web Vitals
model WebVital {
  id             String   @id @default(cuid())
  name           String   // LCP, FID, CLS, FCP, TTFB
  value          Float    // Valor da métrica
  rating         String   // good, needs-improvement, poor
  delta          Float    // Delta da métrica
  metricId       String   // ID único da métrica
  navigationType String   // Tipo de navegação
  url            String   @db.Text // URL onde a métrica foi coletada
  userAgent      String   @db.Text // User agent do navegador
  timestamp      DateTime // Timestamp da coleta
  createdAt      DateTime @default(now())

  @@index([name])
  @@index([rating])
  @@index([timestamp])
  @@index([url])
  @@index([name, timestamp])
  @@index([rating, timestamp])
}

// Modelos para sistema de templates
model Template {
  id          String   @id @default(cuid())
  name        String
  title       String
  description String?  @db.Text
  icon        String?  // Nome do ícone (ex: 'bar-chart', 'piggy-bank')
  isPublic    Boolean  @default(true)
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)
  isNew       Boolean  @default(false)
  popularity  Int      @default(0)
  usageCount  Int      @default(0)
  data        String   @db.Text // JSON com estrutura das sheets
  createdBy   String?  // ID do usuário criador (null para templates do sistema)
  creator     User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relacionamentos
  categories TemplateCategory[]
  reviews    TemplateReview[]
  usage      TemplateUsage[]

  @@index([isPublic])
  @@index([isActive])
  @@index([isFeatured])
  @@index([popularity])
  @@index([createdBy])
  @@index([createdAt])
}

model TemplateCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  icon        String?
  color       String?  // Cor para UI (ex: '#3B82F6')
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relacionamentos
  templates Template[]

  @@index([isActive])
  @@index([sortOrder])
}

model TemplateReview {
  id         String   @id @default(cuid())
  templateId String
  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  rating     Int      // 1-5 estrelas
  comment    String?  @db.Text
  isHelpful  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([templateId, userId]) // Um usuário só pode avaliar um template uma vez
  @@index([templateId])
  @@index([userId])
  @@index([rating])
  @@index([createdAt])
}

model TemplateUsage {
  id         String   @id @default(cuid())
  templateId String
  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  workbookId String?  // ID do workbook criado (se ainda existir)
  workbook   Workbook? @relation(fields: [workbookId], references: [id], onDelete: SetNull)
  createdAt  DateTime @default(now())

  @@index([templateId])
  @@index([userId])
  @@index([workbookId])
  @@index([createdAt])
}