'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Locale, i18nConfig } from '@/config/i18n';
import { useTranslation } from '@/hooks/useTranslation';

/**
 * Tipos para o contexto de localização unificado
 */
type LocaleContextType = {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number | object>) => string;
  tPlural: (key: string, count: number) => string;
  formatNumber: (value: number, options?: Intl.NumberFormatOptions) => string;
  formatDate: (value: Date | string | number, options?: Intl.DateTimeFormatOptions) => string;
  isLoading: boolean;
};

// Criar o contexto unificado com valores padrão
const LocaleContext = createContext<LocaleContextType>({
  locale: i18nConfig.defaultLocale,
  setLocale: () => {},
  t: key => key,
  tPlural: key => key,
  formatNumber: value => String(value),
  formatDate: value => new Date(value).toISOString(),
  isLoading: false,
});

/**
 * Hook para acessar o contexto de localização unificado
 */
export const useLocale = () => {
  const context = useContext(LocaleContext);
  if (!context) {
    throw new Error('useLocale deve ser usado dentro de um LocaleProvider');
  }
  return context;
};

/**
 * Provider unificado que fornece o contexto de idioma para a aplicação
 * Substitui os providers duplicados e oferece funcionalidade completa de i18n
 */
export function LocaleProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(i18nConfig.defaultLocale);
  const [isLoading, setIsLoading] = useState(false);
  const { t, tPlural, formatNumber, formatDate } = useTranslation(locale);

  // Função para alterar o idioma com persistência e validação
  const setLocale = (newLocale: Locale) => {
    if (!i18nConfig.locales.includes(newLocale)) {
      console.warn(`Locale ${newLocale} não é suportado. Usando ${i18nConfig.defaultLocale}.`);
      return;
    }

    setIsLoading(true);

    try {
      // Atualizar estado
      setLocaleState(newLocale);

      // Persistir no localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', newLocale);
      }

      // Atualizar atributo lang do documento
      if (typeof document !== 'undefined') {
        document.documentElement.lang = newLocale;
      }

    } catch (error) {
      console.error('Erro ao alterar locale:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Efeito para inicializar o locale baseado no localStorage ou browser
  useEffect(() => {
    setIsLoading(true);

    try {
      let initialLocale = i18nConfig.defaultLocale;

      // Verificar localStorage primeiro
      if (typeof window !== 'undefined') {
        const savedLocale = localStorage.getItem('locale') as Locale;
        if (savedLocale && i18nConfig.locales.includes(savedLocale)) {
          initialLocale = savedLocale;
        } else {
          // Detectar idioma do browser como fallback
          const browserLocale = navigator.language;
          const matchedLocale = i18nConfig.locales.find(locale => {
            const localePrefix = locale.split('-')[0];
            return localePrefix && browserLocale.startsWith(localePrefix);
          });
          if (matchedLocale) {
            initialLocale = matchedLocale;
          }
        }
      }

      // Configurar locale inicial
      setLocaleState(initialLocale);

      if (typeof document !== 'undefined') {
        document.documentElement.lang = initialLocale;
      }

    } catch (error) {
      console.error('Erro ao inicializar locale:', error);
      // Fallback para locale padrão
      setLocaleState(i18nConfig.defaultLocale);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <LocaleContext.Provider
      value={{
        locale,
        setLocale,
        t,
        tPlural,
        formatNumber,
        formatDate,
        isLoading
      }}
    >
      {children}
    </LocaleContext.Provider>
  );
}

export default LocaleContext;
