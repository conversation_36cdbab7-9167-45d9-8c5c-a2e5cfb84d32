interface CaptureConsoleOptions {
    levels?: string[];
    /**
     * By default, Sentry will mark captured console messages as handled.
     * Set this to `false` if you want to mark them as unhandled instead.
     *
     * @default true
     */
    handled?: boolean;
}
/**
 * Send Console API calls as Sentry Events.
 */
export declare const captureConsoleIntegration: (options?: CaptureConsoleOptions | undefined) => import("../types-hoist/integration").Integration;
export {};
//# sourceMappingURL=captureconsole.d.ts.map
