import type { Carrier } from './../carrier';
import type { AsyncContextStrategy } from './types';
/**
 * @private Private API with no semver guarantees!
 *
 * Sets the global async context strategy
 */
export declare function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void;
/**
 * Get the current async context strategy.
 * If none has been setup, the default will be used.
 */
export declare function getAsyncContextStrategy(carrier: Carrier): AsyncContextStrategy;
//# sourceMappingURL=index.d.ts.map