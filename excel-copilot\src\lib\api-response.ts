import { NextResponse } from 'next/server';

/**
 * Utilitário para padronizar respostas da API
 */
export class ApiResponse {
  /**
   * Resposta de sucesso
   */
  static success<T>(data: T, status: number = 200) {
    return NextResponse.json(
      {
        success: true,
        data,
      },
      { status }
    );
  }

  /**
   * Resposta de erro
   */
  static error(message: string, status: number = 400, details?: any) {
    return NextResponse.json(
      {
        success: false,
        error: {
          message,
          ...(details && { details }),
        },
      },
      { status }
    );
  }

  /**
   * Resposta de validação
   */
  static validation(errors: Record<string, string[]>) {
    return NextResponse.json(
      {
        success: false,
        error: {
          message: 'Dados inválidos',
          validation: errors,
        },
      },
      { status: 422 }
    );
  }

  /**
   * Resposta de não autorizado
   */
  static unauthorized(message: string = 'Não autorizado') {
    return NextResponse.json(
      {
        success: false,
        error: {
          message,
        },
      },
      { status: 401 }
    );
  }

  /**
   * Resposta de não encontrado
   */
  static notFound(message: string = 'Recurso não encontrado') {
    return NextResponse.json(
      {
        success: false,
        error: {
          message,
        },
      },
      { status: 404 }
    );
  }

  /**
   * Resposta de conflito
   */
  static conflict(message: string = 'Conflito de dados') {
    return NextResponse.json(
      {
        success: false,
        error: {
          message,
        },
      },
      { status: 409 }
    );
  }

  /**
   * Resposta de erro interno
   */
  static internal(message: string = 'Erro interno do servidor') {
    return NextResponse.json(
      {
        success: false,
        error: {
          message,
        },
      },
      { status: 500 }
    );
  }
}
