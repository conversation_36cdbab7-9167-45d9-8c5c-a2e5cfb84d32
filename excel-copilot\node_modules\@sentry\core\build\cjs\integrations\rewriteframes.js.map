{"version": 3, "file": "rewriteframes.js", "sources": ["../../../src/integrations/rewriteframes.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { Event } from '../types-hoist/event';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport type { Stacktrace } from '../types-hoist/stacktrace';\nimport { basename, relative } from '../utils-hoist/path';\nimport { GLOBAL_OBJ } from '../utils-hoist/worldwide';\n\ntype StackFrameIteratee = (frame: StackFrame) => StackFrame;\n\nconst INTEGRATION_NAME = 'RewriteFrames';\n\ninterface RewriteFramesOptions {\n  /**\n   * Root path (the beginning of the path) that will be stripped from the frames' filename.\n   *\n   * This option has slightly different behaviour in the browser and on servers:\n   * - In the browser, the value you provide in `root` will be stripped from the beginning stack frames' paths (if the path started with the value).\n   * - On the server, the root value will only replace the beginning of stack frame filepaths, when the path is absolute. If no `root` value is provided and the path is absolute, the frame will be reduced to only the filename and the provided `prefix` option.\n   *\n   * Browser example:\n   * - Original frame: `'http://example.com/my/path/static/asset.js'`\n   * - `root: 'http://example.com/my/path'`\n   * - `assetPrefix: 'app://'`\n   * - Resulting frame: `'app:///static/asset.js'`\n   *\n   * Server example:\n   * - Original frame: `'/User/local/my/path/static/asset.js'`\n   * - `root: '/User/local/my/path'`\n   * - `assetPrefix: 'app://'`\n   * - Resulting frame: `'app:///static/asset.js'`\n   */\n  root?: string;\n\n  /**\n   * A custom prefix that stack frames will be prepended with.\n   *\n   * Default: `'app://'`\n   *\n   * This option has slightly different behaviour in the browser and on servers:\n   * - In the browser, the value you provide in `prefix` will prefix the resulting filename when the value you provided in `root` was applied. Effectively replacing whatever `root` matched in the beginning of the frame with `prefix`.\n   * - On the server, the prefix is applied to all stackframes with absolute paths. On Windows, the drive identifier (e.g. \"C://\") is replaced with the prefix.\n   */\n  prefix?: string;\n\n  /**\n   * Defines an iterator that is used to iterate through all of the stack frames for modification before being sent to Sentry.\n   * Setting this option will effectively disable both the `root` and the `prefix` options.\n   */\n  iteratee?: StackFrameIteratee;\n}\n\n/**\n * Rewrite event frames paths.\n */\nexport const rewriteFramesIntegration = defineIntegration((options: RewriteFramesOptions = {}) => {\n  const root = options.root;\n  const prefix = options.prefix || 'app:///';\n\n  const isBrowser = 'window' in GLOBAL_OBJ && !!GLOBAL_OBJ.window;\n\n  const iteratee: StackFrameIteratee = options.iteratee || generateIteratee({ isBrowser, root, prefix });\n\n  /** Process an exception event. */\n  function _processExceptionsEvent(event: Event): Event {\n    try {\n      return {\n        ...event,\n        exception: {\n          ...event.exception,\n          // The check for this is performed inside `process` call itself, safe to skip here\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          values: event.exception!.values!.map(value => ({\n            ...value,\n            ...(value.stacktrace && { stacktrace: _processStacktrace(value.stacktrace) }),\n          })),\n        },\n      };\n    } catch (_oO) {\n      return event;\n    }\n  }\n\n  /** Process a stack trace. */\n  function _processStacktrace(stacktrace?: Stacktrace): Stacktrace {\n    return {\n      ...stacktrace,\n      frames: stacktrace?.frames && stacktrace.frames.map(f => iteratee(f)),\n    };\n  }\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(originalEvent) {\n      let processedEvent = originalEvent;\n\n      if (originalEvent.exception && Array.isArray(originalEvent.exception.values)) {\n        processedEvent = _processExceptionsEvent(processedEvent);\n      }\n\n      return processedEvent;\n    },\n  };\n});\n\n/**\n * Exported only for tests.\n */\nexport function generateIteratee({\n  isBrowser,\n  root,\n  prefix,\n}: {\n  isBrowser: boolean;\n  root?: string;\n  prefix: string;\n}): StackFrameIteratee {\n  return (frame: StackFrame) => {\n    if (!frame.filename) {\n      return frame;\n    }\n\n    // Determine if this is a Windows frame by checking for a Windows-style prefix such as `C:\\`\n    const isWindowsFrame =\n      /^[a-zA-Z]:\\\\/.test(frame.filename) ||\n      // or the presence of a backslash without a forward slash (which are not allowed on Windows)\n      (frame.filename.includes('\\\\') && !frame.filename.includes('/'));\n\n    // Check if the frame filename begins with `/`\n    const startsWithSlash = /^\\//.test(frame.filename);\n\n    if (isBrowser) {\n      if (root) {\n        const oldFilename = frame.filename;\n        if (oldFilename.indexOf(root) === 0) {\n          frame.filename = oldFilename.replace(root, prefix);\n        }\n      }\n    } else {\n      if (isWindowsFrame || startsWithSlash) {\n        const filename = isWindowsFrame\n          ? frame.filename\n              .replace(/^[a-zA-Z]:/, '') // remove Windows-style prefix\n              .replace(/\\\\/g, '/') // replace all `\\\\` instances with `/`\n          : frame.filename;\n        const base = root ? relative(root, filename) : basename(filename);\n        frame.filename = `${prefix}${base}`;\n      }\n    }\n\n    return frame;\n  };\n}\n"], "names": ["defineIntegration", "GLOBAL_OBJ", "relative", "basename"], "mappings": ";;;;;;AASA,MAAM,gBAAA,GAAmB,eAAe;;AA0CxC;AACA;AACA;AACa,MAAA,wBAAA,GAA2BA,6BAAiB,CAAC,CAAC,OAAO,GAAyB,EAAE,KAAK;AAClG,EAAE,MAAM,IAAA,GAAO,OAAO,CAAC,IAAI;AAC3B,EAAE,MAAM,MAAO,GAAE,OAAO,CAAC,MAAA,IAAU,SAAS;;AAE5C,EAAE,MAAM,SAAU,GAAE,QAAS,IAAGC,oBAAW,IAAG,CAAC,CAACA,oBAAU,CAAC,MAAM;;AAEjE,EAAE,MAAM,QAAQ,GAAuB,OAAO,CAAC,QAAS,IAAG,gBAAgB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAA,EAAQ,CAAC;;AAExG;AACA,EAAE,SAAS,uBAAuB,CAAC,KAAK,EAAgB;AACxD,IAAI,IAAI;AACR,MAAM,OAAO;AACb,QAAQ,GAAG,KAAK;AAChB,QAAQ,SAAS,EAAE;AACnB,UAAU,GAAG,KAAK,CAAC,SAAS;AAC5B;AACA;AACA,UAAU,MAAM,EAAE,KAAK,CAAC,SAAS,CAAE,MAAM,CAAE,GAAG,CAAC,KAAM,KAAI;AACzD,YAAY,GAAG,KAAK;AACpB,YAAY,IAAI,KAAK,CAAC,UAAA,IAAc,EAAE,UAAU,EAAE,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAA,EAAG,CAAC;AACzF,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,KAAK;AAClB;AACA;;AAEA;AACA,EAAE,SAAS,kBAAkB,CAAC,UAAU,EAA2B;AACnE,IAAI,OAAO;AACX,MAAM,GAAG,UAAU;AACnB,MAAM,MAAM,EAAE,UAAU,EAAE,MAAO,IAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAE,IAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3E,KAAK;AACL;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,aAAa,EAAE;AAChC,MAAM,IAAI,cAAe,GAAE,aAAa;;AAExC,MAAM,IAAI,aAAa,CAAC,SAAA,IAAa,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AACpF,QAAQ,cAAe,GAAE,uBAAuB,CAAC,cAAc,CAAC;AAChE;;AAEA,MAAM,OAAO,cAAc;AAC3B,KAAK;AACL,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACO,SAAS,gBAAgB,CAAC;AACjC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,MAAM;AACR;;AAIA,EAAuB;AACvB,EAAE,OAAO,CAAC,KAAK,KAAiB;AAChC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,KAAK;AAClB;;AAEA;AACA,IAAI,MAAM,cAAe;AACzB,MAAM,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAE;AAC1C;AACA,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAA,IAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;;AAEtE;AACA,IAAI,MAAM,eAAgB,GAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;;AAEtD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,MAAM,WAAA,GAAc,KAAK,CAAC,QAAQ;AAC1C,QAAQ,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAA,KAAM,CAAC,EAAE;AAC7C,UAAU,KAAK,CAAC,QAAA,GAAW,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;AAC5D;AACA;AACA,WAAW;AACX,MAAM,IAAI,cAAe,IAAG,eAAe,EAAE;AAC7C,QAAQ,MAAM,WAAW;AACzB,YAAY,KAAK,CAAC;AAClB,eAAe,OAAO,CAAC,YAAY,EAAE,EAAE,CAAA;AACvC,eAAe,OAAO,CAAC,KAAK,EAAE,GAAG,CAAA;AACjC,YAAY,KAAK,CAAC,QAAQ;AAC1B,QAAQ,MAAM,IAAA,GAAO,IAAA,GAAOC,aAAQ,CAAC,IAAI,EAAE,QAAQ,CAAE,GAAEC,aAAQ,CAAC,QAAQ,CAAC;AACzE,QAAQ,KAAK,CAAC,QAAS,GAAE,CAAC,EAAA,MAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA;;AAEA,IAAA,OAAA,KAAA;AACA,GAAA;AACA;;;;;"}