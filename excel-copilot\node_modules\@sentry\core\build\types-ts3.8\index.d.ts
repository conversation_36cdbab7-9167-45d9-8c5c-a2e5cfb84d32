export { ClientClass as SentryCoreCurrentScopes } from './sdk';
export { AsyncContextStrategy } from './asyncContext/types';
export { Carrier } from './carrier';
export { OfflineStore, OfflineTransportOptions } from './transports/offline';
export { ServerRuntimeClientOptions } from './server-runtime-client';
export { IntegrationIndex } from './integration';
export * from './tracing';
export * from './semanticAttributes';
export { createEventEnvelope, createSessionEnvelope, createSpanEnvelope } from './envelope';
export { captureCheckIn, withMonitor, captureException, captureEvent, captureMessage, lastEventId, close, flush, setContext, setExtra, setExtras, setTag, setTags, setUser, isInitialized, isEnabled, startSession, endSession, captureSession, addEventProcessor, } from './exports';
export { getCurrentScope, getIsolationScope, getGlobalScope, withScope, withIsolationScope, getClient, getTraceContextFromScope, } from './currentScopes';
export { getDefaultCurrentScope, getDefaultIsolationScope } from './defaultScopes';
export { setAsyncContextStrategy } from './asyncContext';
export { getGlobalSingleton, getMainCarrier } from './carrier';
export { makeSession, closeSession, updateSession } from './session';
export { Scope } from './scope';
export { CaptureContext, ScopeContext, ScopeData } from './scope';
export { notifyEventProcessors } from './eventProcessors';
export { getEnvelopeEndpointWithUrlEncodedAuth, getReportDialogEndpoint } from './api';
export { Client, BaseClient, } from './client';
export { ServerRuntimeClient } from './server-runtime-client';
export { initAndBind, setCurrentClient } from './sdk';
export { createTransport } from './transports/base';
export { makeOfflineTransport } from './transports/offline';
export { makeMultiplexedTransport } from './transports/multiplexed';
export { getIntegrationsToSetup, addIntegration, defineIntegration } from './integration';
export { applyScopeDataToEvent, mergeScopeData } from './utils/applyScopeDataToEvent';
export { prepareEvent } from './utils/prepareEvent';
export { createCheckInEnvelope } from './checkin';
export { hasTracingEnabled } from './utils/hasSpansEnabled';
export { hasSpansEnabled } from './utils/hasSpansEnabled';
export { isSentryRequestUrl } from './utils/isSentryRequestUrl';
export { handleCallbackErrors } from './utils/handleCallbackErrors';
export { parameterize, fmt } from './utils/parameterize';
export { addAutoIpAddressToSession, addAutoIpAddressToUser } from './utils/ipAddress';
export { convertSpanLinksForEnvelope, spanToTraceHeader, spanToJSON, spanIsSampled, spanToTraceContext, getSpanDescendants, getStatusMessage, getRootSpan, getActiveSpan, addChildSpanToSpan, spanTimeInputToSeconds, updateSpanName, } from './utils/spanUtils';
export { parseSampleRate } from './utils/parseSampleRate';
export { applySdkMetadata } from './utils/sdkMetadata';
export { getTraceData } from './utils/traceData';
export { getTraceMetaTags } from './utils/meta';
export { winterCGHeadersToDict, winterCGRequestToRequestData, httpRequestToRequestData, extractQueryParamsFromUrl, headersToDict, } from './utils/request';
export { DEFAULT_ENVIRONMENT } from './constants';
export { addBreadcrumb } from './breadcrumbs';
export { functionToStringIntegration } from './integrations/functiontostring';
export { inboundFiltersIntegration } from './integrations/eventFilters';
export { eventFiltersIntegration } from './integrations/eventFilters';
export { linkedErrorsIntegration } from './integrations/linkederrors';
export { moduleMetadataIntegration } from './integrations/metadata';
export { requestDataIntegration } from './integrations/requestdata';
export { captureConsoleIntegration } from './integrations/captureconsole';
export { dedupeIntegration } from './integrations/dedupe';
export { extraErrorDataIntegration } from './integrations/extraerrordata';
export { rewriteFramesIntegration } from './integrations/rewriteframes';
export { supabaseIntegration, instrumentSupabaseClient } from './integrations/supabase';
export { zodErrorsIntegration } from './integrations/zoderrors';
export { thirdPartyErrorFilterIntegration } from './integrations/third-party-errors-filter';
export { consoleIntegration } from './integrations/console';
export { profiler } from './profiling';
export { instrumentFetchRequest } from './fetch';
export { trpcMiddleware } from './trpc';
export { wrapMcpServerWithSentry } from './mcp-server';
export { captureFeedback } from './feedback';
export { ReportDialogOptions } from './report-dialog';
export { _INTERNAL_captureLog, _INTERNAL_flushLogsBuffer, _INTERNAL_captureSerializedLog } from './logs/exports';
export { consoleLoggingIntegration } from './logs/console-integration';
export { FeatureFlag } from './featureFlags';
export { applyAggregateErrorsToEvent } from './utils-hoist/aggregate-errors';
export { getBreadcrumbLogLevelFromHttpStatusCode } from './utils-hoist/breadcrumb-log-level';
export { getComponentName, getLocationHref, htmlTreeAsString } from './utils-hoist/browser';
export { dsnFromString, dsnToString, makeDsn } from './utils-hoist/dsn';
export { SentryError } from './utils-hoist/error';
export { GLOBAL_OBJ } from './utils-hoist/worldwide';
export { InternalGlobal } from './utils-hoist/worldwide';
export { addConsoleInstrumentationHandler } from './utils-hoist/instrument/console';
export { addFetchEndInstrumentationHandler, addFetchInstrumentationHandler } from './utils-hoist/instrument/fetch';
export { addGlobalErrorInstrumentationHandler } from './utils-hoist/instrument/globalError';
export { addGlobalUnhandledRejectionInstrumentationHandler } from './utils-hoist/instrument/globalUnhandledRejection';
export { addHandler, maybeInstrument, resetInstrumentationHandlers, triggerHandlers, } from './utils-hoist/instrument/handlers';
export { isDOMError, isDOMException, isElement, isError, isErrorEvent, isEvent, isInstanceOf, isParameterizedString, isPlainObject, isPrimitive, isRegExp, isString, isSyntheticEvent, isThenable, isVueViewModel, } from './utils-hoist/is';
export { isBrowser } from './utils-hoist/isBrowser';
export { CONSOLE_LEVELS, consoleSandbox, logger, originalConsoleMethods } from './utils-hoist/logger';
export { Logger } from './utils-hoist/logger';
export { addContextToFrame, addExceptionMechanism, addExceptionTypeValue, checkOrSetAlreadyCaught, getEventDescription, parseSemver, uuid4, } from './utils-hoist/misc';
export { isNodeEnv, loadModule } from './utils-hoist/node';
export { normalize, normalizeToSize, normalizeUrlToBase } from './utils-hoist/normalize';
export { addNonEnumerableProperty, convertToPlainObject, dropUndefinedKeys, extractExceptionKeysForMessage, fill, getOriginalFunction, markFunctionWrapped, objectify, } from './utils-hoist/object';
export { basename, dirname, isAbsolute, join, normalizePath, relative, resolve } from './utils-hoist/path';
export { makePromiseBuffer, SENTRY_BUFFER_FULL_ERROR } from './utils-hoist/promisebuffer';
export { PromiseBuffer } from './utils-hoist/promisebuffer';
export { severityLevelFromString } from './utils-hoist/severity';
export { UNKNOWN_FUNCTION, createStackParser, getFramesFromEvent, getFunctionName, stackParserFromStackParserOptions, stripSentryFramesAndReverse, } from './utils-hoist/stacktrace';
export { filenameIsInApp, node, nodeStackLineParser } from './utils-hoist/node-stack-trace';
export { isMatchingPattern, safeJoin, snipLine, stringMatchesSomePattern, truncate } from './utils-hoist/string';
export { isNativeFunction, supportsDOMError, supportsDOMException, supportsErrorEvent, supportsFetch, supportsHistory, supportsNativeFetch, supportsReferrerPolicy, supportsReportingObserver, } from './utils-hoist/supports';
export { SyncPromise, rejectedSyncPromise, resolvedSyncPromise } from './utils-hoist/syncpromise';
export { browserPerformanceTimeOrigin, dateTimestampInSeconds, timestampInSeconds } from './utils-hoist/time';
export { TRACEPARENT_REGEXP, extractTraceparentData, generateSentryTraceHeader, propagationContextFromHeaders, } from './utils-hoist/tracing';
export { getSDKSource, isBrowserBundle } from './utils-hoist/env';
export { SdkSource } from './utils-hoist/env';
export { addItemToEnvelope, createAttachmentEnvelopeItem, createEnvelope, createEventEnvelopeHeaders, createSpanEnvelopeItem, envelopeContainsItemType, envelopeItemTypeToDataCategory, forEachEnvelopeItem, getSdkMetadataForEnvelopeHeader, parseEnvelope, serializeEnvelope, } from './utils-hoist/envelope';
export { createClientReportEnvelope } from './utils-hoist/clientreport';
export { DEFAULT_RETRY_AFTER, disabledUntil, isRateLimited, parseRetryAfterHeader, updateRateLimits, } from './utils-hoist/ratelimit';
export { RateLimits } from './utils-hoist/ratelimit';
export { MAX_BAGGAGE_STRING_LENGTH, SENTRY_BAGGAGE_KEY_PREFIX, SENTRY_BAGGAGE_KEY_PREFIX_REGEX, baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader, parseBaggageHeader, objectToBaggageHeader, } from './utils-hoist/baggage';
export { getSanitizedUrlString, parseUrl, stripUrlQueryAndFragment, parseStringToURLObject, getHttpSpanDetailsFromUrlObject, isURLObjectRelative, getSanitizedUrlStringFromUrlObject, } from './utils-hoist/url';
export { eventFromMessage, eventFromUnknownInput, exceptionFromError, parseStackFrames, } from './utils-hoist/eventbuilder';
export { callFrameToStackFrame, watchdogTimer } from './utils-hoist/anr';
export { LRUMap } from './utils-hoist/lru';
export { generateTraceId, generateSpanId } from './utils-hoist/propagationContext';
export { vercelWaitUntil } from './utils-hoist/vercelWaitUntil';
export { SDK_VERSION } from './utils-hoist/version';
export { getDebugImagesForResources, getFilenameToDebugIdMap } from './utils-hoist/debug-ids';
export { escapeStringForRegex } from './utils-hoist/vendor/escapeStringForRegex';
export { Attachment } from './types-hoist/attachment';
export { Breadcrumb, BreadcrumbHint, FetchBreadcrumbData, XhrBreadcrumbData, FetchBreadcrumbHint, XhrBreadcrumbHint, } from './types-hoist/breadcrumb';
export { ClientReport, Outcome, EventDropReason } from './types-hoist/clientreport';
export { Context, Contexts, DeviceContext, OsContext, AppContext, CultureContext, TraceContext, CloudResourceContext, MissingInstrumentationContext, } from './types-hoist/context';
export { DataCategory } from './types-hoist/datacategory';
export { DsnComponents, DsnLike, DsnProtocol } from './types-hoist/dsn';
export { DebugImage, DebugMeta } from './types-hoist/debugMeta';
export { AttachmentItem, BaseEnvelopeHeaders, BaseEnvelopeItemHeaders, ClientReportEnvelope, ClientReportItem, DynamicSamplingContext, Envelope, EnvelopeItemType, EnvelopeItem, EventEnvelope, EventEnvelopeHeaders, EventItem, ReplayEnvelope, FeedbackItem, SessionEnvelope, SessionItem, UserFeedbackItem, CheckInItem, CheckInEnvelope, RawSecurityEnvelope, RawSecurityItem, ProfileItem, ProfileChunkEnvelope, ProfileChunkItem, SpanEnvelope, SpanItem, LogEnvelope, } from './types-hoist/envelope';
export { ExtendedError } from './types-hoist/error';
export { Event, EventHint, EventType, ErrorEvent, TransactionEvent } from './types-hoist/event';
export { EventProcessor } from './types-hoist/eventprocessor';
export { Exception } from './types-hoist/exception';
export { Extra, Extras } from './types-hoist/extra';
export { Integration, IntegrationFn } from './types-hoist/integration';
export { Mechanism } from './types-hoist/mechanism';
export { ExtractedNodeRequestData, HttpHeaderValue, Primitive, WorkerLocation } from './types-hoist/misc';
export { ClientOptions, Options } from './types-hoist/options';
export { Package } from './types-hoist/package';
export { PolymorphicEvent, PolymorphicRequest } from './types-hoist/polymorphics';
export { ThreadId, FrameId, StackId, ThreadCpuSample, ThreadCpuStack, ThreadCpuFrame, ThreadCpuProfile, ContinuousThreadCpuProfile, Profile, ProfileChunk, } from './types-hoist/profiling';
export { ReplayEvent, ReplayRecordingData, ReplayRecordingMode } from './types-hoist/replay';
export { FeedbackEvent, FeedbackFormData, FeedbackInternalOptions, FeedbackModalIntegration, FeedbackScreenshotIntegration, SendFeedback, SendFeedbackParams, UserFeedback, } from './types-hoist/feedback';
export { QueryParams, RequestEventData, SanitizedRequestData } from './types-hoist/request';
export { Runtime } from './types-hoist/runtime';
export { SdkInfo } from './types-hoist/sdkinfo';
export { SdkMetadata } from './types-hoist/sdkmetadata';
export { SessionAggregates, AggregationCounts, Session, SessionContext, SessionStatus, SerializedSession, } from './types-hoist/session';
export { SeverityLevel } from './types-hoist/severity';
export { Span, SentrySpanArguments, SpanOrigin, SpanAttributeValue, SpanAttributes, SpanTimeInput, SpanJSON, SpanContextData, TraceFlag, } from './types-hoist/span';
export { SpanStatus } from './types-hoist/spanStatus';
export { Log, LogSeverityLevel } from './types-hoist/log';
export { TimedEvent } from './types-hoist/timedEvent';
export { StackFrame } from './types-hoist/stackframe';
export { Stacktrace, StackParser, StackLineParser, StackLineParserFn } from './types-hoist/stacktrace';
export { PropagationContext, TracePropagationTargets, SerializedTraceData } from './types-hoist/tracing';
export { StartSpanOptions } from './types-hoist/startSpanOptions';
export { TraceparentData, TransactionSource } from './types-hoist/transaction';
export { CustomSamplingContext, SamplingContext } from './types-hoist/samplingcontext';
export { DurationUnit, InformationUnit, FractionUnit, MeasurementUnit, NoneUnit, Measurements, } from './types-hoist/measurement';
export { Thread } from './types-hoist/thread';
export { Transport, TransportRequest, TransportMakeRequestResponse, InternalBaseTransportOptions, BaseTransportOptions, TransportRequestExecutor, } from './types-hoist/transport';
export { User } from './types-hoist/user';
export { WebFetchHeaders, WebFetchRequest } from './types-hoist/webfetchapi';
export { WrappedFunction } from './types-hoist/wrappedfunction';
export { HandlerDataFetch, HandlerDataXhr, HandlerDataDom, HandlerDataConsole, HandlerDataHistory, HandlerDataError, HandlerDataUnhandledRejection, ConsoleLevel, SentryXhrData, SentryWrappedXMLHttpRequest, } from './types-hoist/instrument';
export { BrowserClientReplayOptions, BrowserClientProfilingOptions } from './types-hoist/browseroptions';
export { CheckIn, MonitorConfig, FinishedCheckIn, InProgressCheckIn, SerializedCheckIn, } from './types-hoist/checkin';
export { ParameterizedString } from './types-hoist/parameterize';
export { ContinuousProfiler, ProfilingIntegration, Profiler } from './types-hoist/profiling';
export { ViewHierarchyData, ViewHierarchyWindow } from './types-hoist/view-hierarchy';
export { LegacyCSPReport } from './types-hoist/csp';
export { SerializedLog, SerializedLogContainer } from './types-hoist/log';
//# sourceMappingURL=index.d.ts.map
