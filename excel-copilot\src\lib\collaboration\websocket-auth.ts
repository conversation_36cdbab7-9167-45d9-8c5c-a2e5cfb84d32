import * as jwt from 'jsonwebtoken';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

/**
 * Sistema de autenticação JWT robusta para WebSocket
 * Implementa validação de tokens, verificação de permissões e rate limiting
 */

export interface WebSocketAuthPayload {
  userId: string;
  userName: string;
  userEmail: string;
  workbookId: string;
  permissions: string[];
  iat: number;
  exp: number;
}

export interface AuthResult {
  success: boolean;
  payload?: WebSocketAuthPayload;
  error?: string;
  shouldBlock?: boolean;
}

export interface WebSocketPermissions {
  canRead: boolean;
  canWrite: boolean;
  canShare: boolean;
  canAdmin: boolean;
}

export class WebSocketAuthenticator {
  private readonly JWT_SECRET: string;
  private readonly TOKEN_EXPIRY = '1h'; // 1 hora
  private readonly REFRESH_THRESHOLD = 15 * 60 * 1000; // 15 minutos
  private blockedTokens: Set<string> = new Set();
  private activeConnections: Map<string, Set<string>> = new Map(); // userId -> Set<socketId>

  constructor() {
    this.JWT_SECRET = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET || 'fallback-secret';
    
    if (!process.env.NEXTAUTH_SECRET && !process.env.JWT_SECRET) {
      logger.warn('JWT_SECRET não configurado, usando fallback inseguro');
    }

    // Limpeza periódica de tokens bloqueados
    setInterval(() => {
      this.cleanupBlockedTokens();
    }, 60 * 60 * 1000); // 1 hora
  }

  /**
   * Gera token JWT para WebSocket
   */
  async generateWebSocketToken(
    userId: string,
    userName: string,
    userEmail: string,
    workbookId: string
  ): Promise<string> {
    try {
      // Verificar permissões do usuário na planilha
      const permissions = await this.getUserPermissions(userId, workbookId);
      
      const payload: Omit<WebSocketAuthPayload, 'iat' | 'exp'> = {
        userId,
        userName,
        userEmail,
        workbookId,
        permissions: this.serializePermissions(permissions),
      };

      const token = jwt.sign(payload, this.JWT_SECRET, {
        expiresIn: this.TOKEN_EXPIRY,
        issuer: 'excel-copilot',
        audience: 'websocket',
      });

      logger.info('Token WebSocket gerado', {
        userId,
        workbookId,
        permissions: permissions,
      });

      return token;
    } catch (error) {
      logger.error('Erro ao gerar token WebSocket', {
        userId,
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      throw new Error('Falha na geração do token');
    }
  }

  /**
   * Valida token JWT do WebSocket
   */
  async validateWebSocketToken(token: string, socketId: string): Promise<AuthResult> {
    try {
      // Verificar se token está bloqueado
      if (this.blockedTokens.has(token)) {
        return {
          success: false,
          error: 'Token bloqueado',
          shouldBlock: true,
        };
      }

      // Verificar e decodificar token
      const decoded = jwt.verify(token, this.JWT_SECRET, {
        issuer: 'excel-copilot',
        audience: 'websocket',
      }) as WebSocketAuthPayload;

      // Verificar se token está próximo do vencimento
      const now = Date.now() / 1000;
      if (decoded.exp - now < this.REFRESH_THRESHOLD / 1000) {
        logger.warn('Token WebSocket próximo do vencimento', {
          userId: decoded.userId,
          expiresIn: decoded.exp - now,
        });
      }

      // Verificar se usuário ainda tem acesso à planilha
      const hasAccess = await this.verifyWorkbookAccess(decoded.userId, decoded.workbookId);
      if (!hasAccess) {
        return {
          success: false,
          error: 'Acesso à planilha revogado',
          shouldBlock: true,
        };
      }

      // Registrar conexão ativa
      this.registerActiveConnection(decoded.userId, socketId);

      logger.info('Token WebSocket validado com sucesso', {
        userId: decoded.userId,
        workbookId: decoded.workbookId,
        socketId,
      });

      return {
        success: true,
        payload: decoded,
      };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Token WebSocket inválido', {
          error: error.message,
          socketId,
        });
        return {
          success: false,
          error: 'Token inválido',
          shouldBlock: true,
        };
      }

      logger.error('Erro na validação do token WebSocket', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        socketId,
      });

      return {
        success: false,
        error: 'Erro interno de autenticação',
      };
    }
  }

  /**
   * Bloqueia um token específico
   */
  blockToken(token: string, reason: string): void {
    this.blockedTokens.add(token);
    logger.info('Token WebSocket bloqueado', { reason });
  }

  /**
   * Remove conexão ativa
   */
  removeActiveConnection(userId: string, socketId: string): void {
    const connections = this.activeConnections.get(userId);
    if (connections) {
      connections.delete(socketId);
      if (connections.size === 0) {
        this.activeConnections.delete(userId);
      }
    }
  }

  /**
   * Obtém conexões ativas de um usuário
   */
  getActiveConnections(userId: string): string[] {
    const connections = this.activeConnections.get(userId);
    return connections ? Array.from(connections) : [];
  }

  /**
   * Verifica permissões do usuário na planilha
   */
  private async getUserPermissions(userId: string, workbookId: string): Promise<WebSocketPermissions> {
    try {
      const workbook = await prisma.workbook.findFirst({
        where: {
          id: workbookId,
          OR: [
            { userId }, // Proprietário
            {
              shares: {
                some: {
                  sharedWithUserId: userId,
                },
              },
            },
          ],
        },
        include: {
          shares: {
            where: {
              sharedWithUserId: userId,
            },
          },
        },
      });

      if (!workbook) {
        return {
          canRead: false,
          canWrite: false,
          canShare: false,
          canAdmin: false,
        };
      }

      // Se é proprietário, tem todas as permissões
      if (workbook.userId === userId) {
        return {
          canRead: true,
          canWrite: true,
          canShare: true,
          canAdmin: true,
        };
      }

      // Se é compartilhado, verificar nível de permissão
      const share = workbook.shares[0];
      const permission = share?.permissionLevel || 'READ';

      return {
        canRead: true,
        canWrite: permission === 'WRITE' || permission === 'ADMIN',
        canShare: permission === 'ADMIN',
        canAdmin: permission === 'ADMIN',
      };
    } catch (error) {
      logger.error('Erro ao verificar permissões', {
        userId,
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      return {
        canRead: false,
        canWrite: false,
        canShare: false,
        canAdmin: false,
      };
    }
  }

  /**
   * Verifica se usuário ainda tem acesso à planilha
   */
  private async verifyWorkbookAccess(userId: string, workbookId: string): Promise<boolean> {
    try {
      const workbook = await prisma.workbook.findFirst({
        where: {
          id: workbookId,
          OR: [
            { userId },
            {
              shares: {
                some: {
                  sharedWithUserId: userId,
                },
              },
            },
          ],
        },
      });

      return !!workbook;
    } catch (error) {
      logger.error('Erro ao verificar acesso à planilha', {
        userId,
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      return false;
    }
  }

  /**
   * Serializa permissões para o token
   */
  private serializePermissions(permissions: WebSocketPermissions): string[] {
    const perms: string[] = [];
    if (permissions.canRead) perms.push('read');
    if (permissions.canWrite) perms.push('write');
    if (permissions.canShare) perms.push('share');
    if (permissions.canAdmin) perms.push('admin');
    return perms;
  }

  /**
   * Registra conexão ativa
   */
  private registerActiveConnection(userId: string, socketId: string): void {
    if (!this.activeConnections.has(userId)) {
      this.activeConnections.set(userId, new Set());
    }
    this.activeConnections.get(userId)!.add(socketId);
  }

  /**
   * Limpa tokens bloqueados antigos
   */
  private cleanupBlockedTokens(): void {
    // Em uma implementação real, verificaria timestamps dos tokens
    // Por simplicidade, limpa todos os tokens bloqueados periodicamente
    const oldSize = this.blockedTokens.size;
    this.blockedTokens.clear();
    
    if (oldSize > 0) {
      logger.info('Limpeza de tokens bloqueados realizada', {
        tokensRemovidos: oldSize,
      });
    }
  }
}

// Instância singleton do autenticador WebSocket
export const wsAuthenticator = new WebSocketAuthenticator();
