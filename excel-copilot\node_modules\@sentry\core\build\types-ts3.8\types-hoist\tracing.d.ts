import { DynamicSamplingContext } from './envelope';
export type TracePropagationTargets = (string | RegExp)[];
/**
 * `PropagationContext` represents the data from an incoming trace. It should be constructed from incoming trace data,
 * usually represented by `sentry-trace` and `baggage` HTTP headers.
 *
 * There is always a propagation context present in the SDK (or rather on Scopes), holding at least a `traceId`. This is
 * to ensure that there is always a trace we can attach events onto, even if performance monitoring is disabled. If
 * there was no incoming `traceId`, the `traceId` will be generated by the current SDK.
 */
export interface PropagationContext {
    /**
     * Either represents the incoming `traceId` or the `traceId` generated by the current SDK, if there was no incoming trace.
     */
    traceId: string;
    /**
     * A random between 0 an 1 (including 0, excluding 1) used for sampling in the current execution context.
     * This should be newly generated when a new trace is started.
     */
    sampleRand: number;
    /**
     * Represents the sampling decision of the incoming trace.
     *
     * The current SDK should not modify this value!
     */
    sampled?: boolean;
    /**
     * The `parentSpanId` denotes the ID of the incoming client span. If there is no `parentSpanId` on the propagation
     * context, it means that the the incoming trace didn't come from a span.
     *
     * The current SDK should not modify this value!
     */
    parentSpanId?: string;
    /**
     * A span ID that should be used for the `trace` context of various event types, and for propagation of a `parentSpanId` to downstream services, when performance is disabled or when there is no active span.
     * This value should be set by the SDK in an informed way when the same span ID should be used for one unit of execution (e.g. a request, usually tied to the isolation scope).
     * If this value is undefined on the propagation context, the SDK will generate a random span ID for `trace` contexts and trace propagation.
     */
    propagationSpanId?: string;
    /**
     * An undefined dsc in the propagation context means that the current SDK invocation is the head of trace and still free to modify and set the DSC for outgoing requests.
     *
     * The current SDK should not modify this value!
     */
    dsc?: Partial<DynamicSamplingContext>;
}
/**
 * An object holding trace data, like span and trace ids, sampling decision, and dynamic sampling context
 * in a serialized form. Both keys are expected to be used as Http headers or Html meta tags.
 */
export interface SerializedTraceData {
    'sentry-trace'?: string;
    baggage?: string;
}
//# sourceMappingURL=tracing.d.ts.map
