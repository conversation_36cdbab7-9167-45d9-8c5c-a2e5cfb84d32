{"version": 3, "file": "third-party-errors-filter.js", "sources": ["../../../src/integrations/third-party-errors-filter.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport { addMetadataToStackFrames, stripMetadataFromStackFrames } from '../metadata';\nimport type { EventItem } from '../types-hoist/envelope';\nimport type { Event } from '../types-hoist/event';\nimport { forEachEnvelopeItem } from '../utils-hoist/envelope';\nimport { getFramesFromEvent } from '../utils-hoist/stacktrace';\n\ninterface Options {\n  /**\n   * Keys that have been provided in the Sentry bundler plugin via the the `applicationKey` option, identifying your bundles.\n   *\n   * - Webpack plugin: https://www.npmjs.com/package/@sentry/webpack-plugin#applicationkey\n   * - Vite plugin: https://www.npmjs.com/package/@sentry/vite-plugin#applicationkey\n   * - Esbuild plugin: https://www.npmjs.com/package/@sentry/esbuild-plugin#applicationkey\n   * - Rollup plugin: https://www.npmjs.com/package/@sentry/rollup-plugin#applicationkey\n   */\n  filterKeys: string[];\n\n  /**\n   * Defines how the integration should behave. \"Third-Party Stack Frames\" are stack frames that did not come from files marked with a matching bundle key.\n   *\n   * You can define the behaviour with one of 4 modes:\n   * - `drop-error-if-contains-third-party-frames`: Drop error events that contain at least one third-party stack frame.\n   * - `drop-error-if-exclusively-contains-third-party-frames`: Drop error events that exclusively contain third-party stack frames.\n   * - `apply-tag-if-contains-third-party-frames`: Keep all error events, but apply a `third_party_code: true` tag in case the error contains at least one third-party stack frame.\n   * - `apply-tag-if-exclusively-contains-third-party-frames`: Keep all error events, but apply a `third_party_code: true` tag in case the error contains exclusively third-party stack frames.\n   *\n   * If you chose the mode to only apply tags, the tags can then be used in Sentry to filter your issue stream by entering `!third_party_code:True` in the search bar.\n   */\n  behaviour:\n    | 'drop-error-if-contains-third-party-frames'\n    | 'drop-error-if-exclusively-contains-third-party-frames'\n    | 'apply-tag-if-contains-third-party-frames'\n    | 'apply-tag-if-exclusively-contains-third-party-frames';\n}\n\n/**\n * This integration allows you to filter out, or tag error events that do not come from user code marked with a bundle key via the Sentry bundler plugins.\n */\nexport const thirdPartyErrorFilterIntegration = defineIntegration((options: Options) => {\n  return {\n    name: 'ThirdPartyErrorsFilter',\n    setup(client) {\n      // We need to strip metadata from stack frames before sending them to Sentry since these are client side only.\n      // TODO(lforst): Move this cleanup logic into a more central place in the SDK.\n      client.on('beforeEnvelope', envelope => {\n        forEachEnvelopeItem(envelope, (item, type) => {\n          if (type === 'event') {\n            const event = Array.isArray(item) ? (item as EventItem)[1] : undefined;\n\n            if (event) {\n              stripMetadataFromStackFrames(event);\n              item[1] = event;\n            }\n          }\n        });\n      });\n\n      client.on('applyFrameMetadata', event => {\n        // Only apply stack frame metadata to error events\n        if (event.type) {\n          return;\n        }\n\n        const stackParser = client.getOptions().stackParser;\n        addMetadataToStackFrames(stackParser, event);\n      });\n    },\n\n    processEvent(event) {\n      const frameKeys = getBundleKeysForAllFramesWithFilenames(event);\n\n      if (frameKeys) {\n        const arrayMethod =\n          options.behaviour === 'drop-error-if-contains-third-party-frames' ||\n          options.behaviour === 'apply-tag-if-contains-third-party-frames'\n            ? 'some'\n            : 'every';\n\n        const behaviourApplies = frameKeys[arrayMethod](keys => !keys.some(key => options.filterKeys.includes(key)));\n\n        if (behaviourApplies) {\n          const shouldDrop =\n            options.behaviour === 'drop-error-if-contains-third-party-frames' ||\n            options.behaviour === 'drop-error-if-exclusively-contains-third-party-frames';\n          if (shouldDrop) {\n            return null;\n          } else {\n            event.tags = {\n              ...event.tags,\n              third_party_code: true,\n            };\n          }\n        }\n      }\n\n      return event;\n    },\n  };\n});\n\nfunction getBundleKeysForAllFramesWithFilenames(event: Event): string[][] | undefined {\n  const frames = getFramesFromEvent(event);\n\n  if (!frames) {\n    return undefined;\n  }\n\n  return (\n    frames\n      // Exclude frames without a filename since these are likely native code or built-ins\n      .filter(frame => !!frame.filename)\n      .map(frame => {\n        if (frame.module_metadata) {\n          return Object.keys(frame.module_metadata)\n            .filter(key => key.startsWith(BUNDLER_PLUGIN_APP_KEY_PREFIX))\n            .map(key => key.slice(BUNDLER_PLUGIN_APP_KEY_PREFIX.length));\n        }\n        return [];\n      })\n  );\n}\n\nconst BUNDLER_PLUGIN_APP_KEY_PREFIX = '_sentryBundlerPluginAppKey:';\n"], "names": ["defineIntegration", "envelope", "forEachEnvelopeItem", "stripMetadataFromStackFrames", "addMetadataToStackFrames", "getFramesFromEvent"], "mappings": ";;;;;;;AAoCA;AACA;AACA;AACO,MAAM,mCAAmCA,6BAAiB,CAAC,CAAC,OAAO,KAAc;AACxF,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB;AACA;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAEC,cAAY;AAC9C,QAAQC,4BAAmB,CAACD,UAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AACtD,UAAU,IAAI,IAAK,KAAI,OAAO,EAAE;AAChC,YAAY,MAAM,KAAM,GAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAE,GAAE,CAAC,IAAK,GAAc,CAAC,CAAA,GAAI,SAAS;;AAElF,YAAY,IAAI,KAAK,EAAE;AACvB,cAAcE,qCAA4B,CAAC,KAAK,CAAC;AACjD,cAAc,IAAI,CAAC,CAAC,CAAA,GAAI,KAAK;AAC7B;AACA;AACA,SAAS,CAAC;AACV,OAAO,CAAC;;AAER,MAAM,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,SAAS;AAC/C;AACA,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;AACxB,UAAU;AACV;;AAEA,QAAQ,MAAM,cAAc,MAAM,CAAC,UAAU,EAAE,CAAC,WAAW;AAC3D,QAAQC,iCAAwB,CAAC,WAAW,EAAE,KAAK,CAAC;AACpD,OAAO,CAAC;AACR,KAAK;;AAEL,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,SAAU,GAAE,sCAAsC,CAAC,KAAK,CAAC;;AAErE,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,MAAM,WAAY;AAC1B,UAAU,OAAO,CAAC,SAAU,KAAI,2CAA4C;AAC5E,UAAU,OAAO,CAAC,SAAA,KAAc;AAChC,cAAc;AACd,cAAc,OAAO;;AAErB,QAAQ,MAAM,gBAAiB,GAAE,SAAS,CAAC,WAAW,CAAC,CAAC,IAAA,IAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAA,IAAO,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;;AAEpH,QAAQ,IAAI,gBAAgB,EAAE;AAC9B,UAAU,MAAM,UAAW;AAC3B,YAAY,OAAO,CAAC,SAAU,KAAI,2CAA4C;AAC9E,YAAY,OAAO,CAAC,SAAU,KAAI,uDAAuD;AACzF,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,OAAO,IAAI;AACvB,iBAAiB;AACjB,YAAY,KAAK,CAAC,IAAA,GAAO;AACzB,cAAc,GAAG,KAAK,CAAC,IAAI;AAC3B,cAAc,gBAAgB,EAAE,IAAI;AACpC,aAAa;AACb;AACA;AACA;;AAEA,MAAM,OAAO,KAAK;AAClB,KAAK;AACL,GAAG;AACH,CAAC;;AAED,SAAS,sCAAsC,CAAC,KAAK,EAAiC;AACtF,EAAE,MAAM,MAAO,GAAEC,6BAAkB,CAAC,KAAK,CAAC;;AAE1C,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE;AACF,IAAI;AACJ;AACA,OAAO,MAAM,CAAC,KAAA,IAAS,CAAC,CAAC,KAAK,CAAC,QAAQ;AACvC,OAAO,GAAG,CAAC,KAAA,IAAS;AACpB,QAAQ,IAAI,KAAK,CAAC,eAAe,EAAE;AACnC,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe;AAClD,aAAa,MAAM,CAAC,GAAI,IAAG,GAAG,CAAC,UAAU,CAAC,6BAA6B,CAAC;AACxE,aAAa,GAAG,CAAC,GAAA,IAAO,GAAG,CAAC,KAAK,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;AACxE;AACA,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP;AACA;;AAEA,MAAM,6BAAA,GAAgC,6BAA6B;;;;"}