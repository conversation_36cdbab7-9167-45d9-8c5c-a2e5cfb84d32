{"name": "excel-copilot", "version": "0.1.0", "private": true, "prisma": {"seed": "node prisma/seed.js"}, "browser": {"fs": false, "path": false, "os": false, "crypto": false, "stream": false, "http": false, "tls": false, "zlib": false, "https": false, "net": false, "node:events": "events", "node:process": "process/browser", "node:util": "util", "node:stream": "stream-browserify", "node:buffer": "buffer", "node:path": "path-browserify", "node:crypto": "crypto-browserify", "node:http": "stream-http", "node:https": "https-browserify", "node:os": "os-browserify/browser", "node:zlib": "browserify-zlib", "node:assert": "assert"}, "scripts": {"type-check": "tsc --noEmit --pretty --project tsconfig.typecheck.json", "type-check:src": "node scripts/check-types.js", "type-check:issues": "node scripts/check-typescript-issues.js", "dev": "next dev", "build": "npm run clean:build && npx cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --max-semi-space-size=1024\" next build", "build:fast": "node scripts/fast-build.js", "build:safe": "npm run verify-build", "build:memory": "npm run clean:build && npx cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "build:dev": "npm run clean:build && npx cross-env NODE_ENV=development NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "build:skip-validation": "npm run clean:build && npx cross-env DISABLE_ENV_VALIDATION=true NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "build:local": "npm run clean:build && npx cross-env NODE_ENV=development BUILD_MODE=local NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "build:no-static": "npm run clean:build && npx cross-env NODE_ENV=development BUILD_MODE=local DISABLE_STATIC_GENERATION=true NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "clean:build": "rimraf .next && rimraf node_modules/.cache", "verify-build": "node scripts/verify-build.js", "build:vercel": "node scripts/vercel-build.js", "build:vercel:safe": "node scripts/vercel-build-safe.js", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:fix:all": "node scripts/lint-fix-all.js", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,scss,json,md}\"", "format:fix-eol": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\" --end-of-line auto", "lint:fix:old-all": "next lint --fix && eslint --fix --rule \"unused-imports/no-unused-imports: error\" --plugin unused-imports \"src/**/*.{ts,tsx}\"", "lint:fix:phase1": "eslint --fix --rule \"no-useless-escape: error\" --rule \"no-case-declarations: error\" \"src/**/*.{ts,tsx}\"", "lint:fix:phase2": "eslint --fix --rule \"import/order: error\" --rule \"import/no-duplicates: error\" \"src/**/*.{ts,tsx}\"", "lint:fix:phase3": "eslint --fix --rule \"@typescript-eslint/no-explicit-any: warn\" --rule \"@typescript-eslint/no-unused-vars: warn\" \"src/**/*.{ts,tsx}\"", "lint:fix:wizard": "node ./fix-lint.js", "fix:lint": "node ./fix-lint.js", "lint:fix:unused": "node ./fix-all-unused-vars.js", "lint:fix:any": "node ./fix-explicit-any.js", "lint:fix:console": "node ./remove-console-logs.js", "lint:fix:imports": "node ./fix-imports.js", "lint:fix:hooks": "node ./fix-react-hooks.js", "lint:fix:advanced": "npm run lint:fix:unused && npm run lint:fix:any && npm run lint:fix:console && npm run lint:fix:imports", "lint:fix:comprehensive": "npm run lint:fix:advanced && npm run format && npm run lint:fix:hooks", "lint:fix:new-unused": "node ./fix-unused-vars.js", "lint:fix:prettier": "node ./fix-prettier.js", "lint:fix:quick": "npm run lint:fix:new-unused && npm run lint:fix:prettier", "prepare:production": "node scripts/prepare-production.js", "clean:imports": "eslint --fix --rule \"unused-imports/no-unused-imports: error\" --plugin unused-imports \"src/**/*.{ts,tsx}\"", "clean:cache": "node scripts/clean-cache.js", "dev:clean": "npm run clean:cache && npm run dev", "format:code": "node scripts/format-code.js", "fix:unused-vars": "node scripts/fix-unused-vars.js", "lint:full": "npm run fix:unused-vars && npm run format:code && npm run lint:fix", "setup": "node scripts/start-dev.js", "setup:prod": "node scripts/setup-production.js", "check:env": "node scripts/check-env.js", "check:dependencies": "node scripts/check-dependencies.js", "check:external-services": "node scripts/verify-external-services.js", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage", "test:coverage": "jest --coverage", "test:coverage:report": "node scripts/test-coverage-report.js", "test:components": "jest --testPathPattern=__tests__/unit/components", "test:integration": "jest --testPathPattern=__tests__/integration/", "test:init": "jest --testPathPattern=__tests__/init.test.ts", "test:setup": "jest --testPathPattern=__tests__/setup.test.ts", "test:ui": "jest --testPathPattern=__tests__/ui/", "test:ui:watch": "jest --testPathPattern=__tests__/ui/ --watch", "test:visual": "jest --testPathPattern=__tests__/ui/visual-regression.test.tsx", "clean-legacy-ai": "node scripts/clean-legacy-ai.js", "test-ai": "node scripts/test-ai-integration.js", "ai-unify": "npm run clean-legacy-ai && npm run test-ai && npm run lint", "test:e2e": "playwright test", "test:typecheck": "tsc --noEmit --project tsconfig.test.json", "pre:dev": "ts-node scripts/check-services.ts", "pre:start": "NODE_ENV=production ts-node scripts/check-services.ts", "db:generate": "prisma generate", "db:migrate:dev": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "npx ts-node --transpile-only prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "vercel:env:pull": "vercel env pull", "prod:build": "NODE_ENV=production next build", "prod:start": "NODE_ENV=production next start -p %PORT%", "analyze": "npx cross-env ANALYZE=true next build", "dev:safe": "npm run pre:dev && npm run dev", "start:safe": "npm run pre:start && npm run start", "download:resources": "node scripts/download-resources.js", "fix:all": "node scripts/rebuild.js && npm run download:resources && npm run dev:clean", "rebuild": "node scripts/rebuild.js", "create:dirs": "node scripts/create-dirs.js", "pretest:simple": "npm run create:dirs", "prepare": "node scripts/prepare-husky.js", "deploy-vercel": "node scripts/deploy-vercel.js", "vercel-setup": "npm i -g vercel && vercel login", "db:migrate:safe": "node scripts/db-migrate-safe.js", "health:db": "curl http://localhost:3000/api/health/db", "health:db:prod": "curl https://excel-copilot-eight.vercel.app/api/health/db", "vercel:db:setup": "node scripts/vercel-deploy-db.js", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:force-auth": "node scripts/create-auth-tables.js", "test:vertex-ai": "node scripts/test-vertex-ai.js", "verify:vertex-ai": "node scripts/verify-vertex-ai-config.js", "test:ai:unit": "jest --testPathPattern=__tests__/unit/server/vertex-ai.test.ts --forceExit", "test:ai:integration": "jest --testPathPattern=__tests__/integration/vertex-ai.integration.test.ts --forceExit", "test:ai:all": "node scripts/run-ai-tests.js", "test:supabase": "jest --testPathPattern=__tests__/integration/supabase-client.test.ts --forceExit", "test:supabase:all": "jest --testPathPattern=__tests__/integration/supabase* --forceExit", "vercel:upload-credentials": "node scripts/upload-credentials.js", "test:env": "node scripts/test-env.js", "test:relaxed": "node scripts/run-tests-relaxed.js", "test:typecheck:relaxed": "tsc --noEmit --project tsconfig.test.json", "subscription:test-functionality": "npx tsx scripts/test-subscription-functionality.ts", "env:diagnose": "node scripts/diagnose-environment.js"}, "dependencies": {"@auth/core": "0.34.2", "@auth/prisma-adapter": "^2.9.0", "@google-cloud/aiplatform": "^4.1.0", "@google-cloud/vertexai": "^1.10.0", "@hookform/resolvers": "^3.1.0", "@neondatabase/serverless": "^1.0.0", "@next/bundle-analyzer": "^14.2.28", "@prisma/adapter-neon": "^6.8.2", "@prisma/client": "^5.6.0", "@prisma/extension-accelerate": "^2.0.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/nextjs": "^9.25.1", "@stripe/stripe-js": "^7.2.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^4.36.1", "@tanstack/react-virtual": "^3.0.0", "@trpc/client": "^10.45.2", "@trpc/next": "^10.43.3", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/bull": "^3.15.9", "@types/dompurify": "^3.0.5", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.10", "@upstash/redis": "^1.34.9", "@vercel/analytics": "^1.5.0", "ai": "^4.3.9", "assert": "^2.1.0", "autoprefixer": "^10.0.1", "axios": "^1.6.1", "babel-plugin-module-resolver": "^5.0.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "bull": "^4.16.5", "chalk": "^5.3.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "critters": "^0.0.23", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "events": "^3.3.0", "exceljs": "^4.4.0", "framer-motion": "^12.9.2", "https-browserify": "^1.0.0", "immer": "^10.1.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.292.0", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "pino": "^8.18.0", "postcss": "8.4.31", "process": "^0.11.10", "querystring-es3": "^0.2.1", "react": "^18", "react-chartjs-2": "^5.3.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.5.2", "react-type-animation": "^3.2.0", "recharts": "^2.15.3", "rimraf": "^5.0.5", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "sonner": "^2.0.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "stripe": "^18.0.0", "superjson": "^2.2.1", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "url": "^0.11.4", "util": "^0.12.5", "uuid": "^9.0.1", "web-vitals": "^5.0.2", "zod": "^3.24.3", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@eslint/js": "^9.25.1", "@playwright/test": "^1.41.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/electron": "^1.4.38", "@types/electron-store": "^1.3.1", "@types/exceljs": "^0.5.3", "@types/jest": "^29.5.14", "@types/node": "^20.17.46", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.1", "babel-eslint": "^10.1.0", "babel-preset-next": "^1.4.0", "cross-env": "^7.0.3", "electron": "^35.2.1", "electron-store": "^10.0.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.28", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^9.3.5", "globals": "^16.0.0", "husky": "^8.0.0", "intl-messageformat": "^10.7.16", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.1", "msw": "^2.7.5", "next": "14.2.3", "node-fetch": "^2.7.0", "node-mocks-http": "^1.16.2", "pino-pretty": "^13.0.0", "prettier": "^3.1.0", "prisma": "^5.6.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vitest": "^3.1.2"}}