import type { Event, EventHint } from './types-hoist/event';
import type { EventProcessor } from './types-hoist/eventprocessor';
/**
 * Process an array of event processors, returning the processed event (or `null` if the event was dropped).
 */
export declare function notifyEventProcessors(processors: EventProcessor[], event: Event | null, hint: EventHint, index?: number): PromiseLike<Event | null>;
//# sourceMappingURL=eventProcessors.d.ts.map