export type ViewHierarchyWindow = {
    alpha: number;
    height: number;
    type: string;
    visible: boolean;
    width: number;
    x: number;
    y: number;
    z?: number;
    children?: ViewHierarchyWindow[];
    depth?: number;
    identifier?: string;
} & Record<string, string | number | boolean>;
export type ViewHierarchyData = {
    rendering_system: string;
    windows: ViewHierarchyWindow[];
};
//# sourceMappingURL=view-hierarchy.d.ts.map