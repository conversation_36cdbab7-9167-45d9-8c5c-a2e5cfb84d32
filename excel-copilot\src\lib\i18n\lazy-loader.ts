import { Locale } from '@/config/i18n';

// Interface para namespace de traduções
export interface TranslationNamespace {
  [key: string]: string | TranslationNamespace;
}

// Cache para namespaces carregados
const namespaceCache = new Map<string, TranslationNamespace>();
const loadingPromises = new Map<string, Promise<TranslationNamespace>>();
const MAX_NAMESPACE_CACHE_SIZE = 50;

// Configuração de namespaces disponíveis
export const AVAILABLE_NAMESPACES = [
  'common',
  'nav',
  'home',
  'dashboard',
  'workbook',
  'auth',
  'errors',
  'forms',
  'actions',
  'messages',
] as const;

export type NamespaceKey = typeof AVAILABLE_NAMESPACES[number];

/**
 * Carrega um namespace de tradução dinamicamente
 * @param namespace Nome do namespace
 * @param locale Locale a ser carregado
 * @returns Promise com as traduções do namespace
 */
export async function loadNamespace(
  namespace: NamespaceKey,
  locale: Locale
): Promise<TranslationNamespace> {
  const cacheKey = `${locale}:${namespace}`;
  
  // Verificar cache primeiro
  if (namespaceCache.has(cacheKey)) {
    return namespaceCache.get(cacheKey)!;
  }
  
  // Verificar se já está carregando
  if (loadingPromises.has(cacheKey)) {
    return loadingPromises.get(cacheKey)!;
  }
  
  // Criar promise de carregamento
  const loadingPromise = loadNamespaceFromSource(namespace, locale);
  loadingPromises.set(cacheKey, loadingPromise);
  
  try {
    const translations = await loadingPromise;
    
    // Gerenciar tamanho do cache
    if (namespaceCache.size >= MAX_NAMESPACE_CACHE_SIZE) {
      // Remover os primeiros 20% dos itens (FIFO)
      const itemsToRemove = Math.floor(MAX_NAMESPACE_CACHE_SIZE * 0.2);
      const keysToRemove = Array.from(namespaceCache.keys()).slice(0, itemsToRemove);
      keysToRemove.forEach(keyToRemove => namespaceCache.delete(keyToRemove));
    }
    
    // Adicionar ao cache
    namespaceCache.set(cacheKey, translations);
    
    return translations;
  } finally {
    // Remover da lista de carregamento
    loadingPromises.delete(cacheKey);
  }
}

/**
 * Carrega as traduções de um namespace específico
 * @param namespace Nome do namespace
 * @param locale Locale a ser carregado
 * @returns Promise com as traduções
 */
async function loadNamespaceFromSource(
  namespace: NamespaceKey,
  locale: Locale
): Promise<TranslationNamespace> {
  try {
    // Simular carregamento dinâmico (em uma implementação real, isso viria de arquivos separados)
    const translations = await getNamespaceTranslations(namespace, locale);
    return translations;
  } catch (error) {
    console.error(`Erro ao carregar namespace ${namespace} para locale ${locale}:`, error);
    // Fallback para namespace vazio
    return {};
  }
}

/**
 * Obtém as traduções de um namespace específico
 * @param namespace Nome do namespace
 * @param locale Locale
 * @returns Traduções do namespace
 */
async function getNamespaceTranslations(
  namespace: NamespaceKey,
  locale: Locale
): Promise<TranslationNamespace> {
  // Em uma implementação real, isso carregaria de arquivos separados
  // Por enquanto, vamos usar as traduções existentes organizadas por namespace
  
  const allTranslations = await import('@/config/i18n');
  const messages = allTranslations.getMessages(locale);
  
  // Extrair apenas o namespace solicitado
  if (namespace in messages) {
    return (messages as any)[namespace];
  }
  
  return {};
}

/**
 * Pré-carrega namespaces críticos para melhor performance
 * @param locale Locale a ser pré-carregado
 * @param namespaces Lista de namespaces para pré-carregar
 */
export async function preloadNamespaces(
  locale: Locale,
  namespaces: NamespaceKey[] = ['common', 'nav', 'actions']
): Promise<void> {
  const preloadPromises = namespaces.map(namespace => 
    loadNamespace(namespace, locale).catch(error => {
      console.warn(`Falha ao pré-carregar namespace ${namespace}:`, error);
    })
  );
  
  await Promise.allSettled(preloadPromises);
}

/**
 * Limpa o cache de namespaces
 */
export function clearNamespaceCache(): void {
  namespaceCache.clear();
  loadingPromises.clear();
}

/**
 * Obtém estatísticas do cache de namespaces
 */
export function getNamespaceCacheStats() {
  return {
    size: namespaceCache.size,
    maxSize: MAX_NAMESPACE_CACHE_SIZE,
    loadingCount: loadingPromises.size,
    cachedNamespaces: Array.from(namespaceCache.keys()),
    memoryUsage: `~${Math.round(namespaceCache.size * 2)}KB`, // Estimativa
  };
}

/**
 * Hook para usar traduções com lazy loading
 * @param namespace Namespace a ser carregado
 * @param locale Locale atual
 * @returns Objeto com traduções e estado de carregamento
 */
export function useLazyTranslation(namespace: NamespaceKey, locale: Locale) {
  const [translations, setTranslations] = useState<TranslationNamespace>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    let isMounted = true;
    
    const loadTranslations = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const namespaceTranslations = await loadNamespace(namespace, locale);
        
        if (isMounted) {
          setTranslations(namespaceTranslations);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Erro desconhecido'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    loadTranslations();
    
    return () => {
      isMounted = false;
    };
  }, [namespace, locale]);
  
  return { translations, isLoading, error };
}

// Importações necessárias para o hook
import { useState, useEffect } from 'react';
