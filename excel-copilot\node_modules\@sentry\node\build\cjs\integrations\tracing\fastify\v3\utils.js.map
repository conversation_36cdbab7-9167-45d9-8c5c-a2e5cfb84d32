{"version": 3, "file": "utils.js", "sources": ["../../../../../../src/integrations/tracing/fastify/v3/utils.ts"], "sourcesContent": ["// Vendored from: https://github.com/open-telemetry/opentelemetry-js-contrib/blob/407f61591ba69a39a6908264379d4d98a48dbec4/plugins/node/opentelemetry-instrumentation-fastify/src/utils.ts\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable @typescript-eslint/no-dynamic-delete */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type Attributes, type Span, type Tracer, SpanStatusCode } from '@opentelemetry/api';\nimport { spanRequestSymbol } from './constants';\nimport type { PluginFastifyReply } from './internal-types';\n\n/**\n * Starts Span\n * @param reply - reply function\n * @param tracer - tracer\n * @param spanName - span name\n * @param spanAttributes - span attributes\n */\nexport function startSpan(\n  reply: PluginFastifyReply,\n  tracer: Tracer,\n  spanName: string,\n  spanAttributes: Attributes = {},\n) {\n  const span = tracer.startSpan(spanName, { attributes: spanAttributes });\n\n  const spans: Span[] = reply[spanRequestSymbol] || [];\n  spans.push(span);\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  Object.defineProperty(reply, spanRequestSymbol, {\n    enumerable: false,\n    configurable: true,\n    value: spans,\n  });\n\n  return span;\n}\n\n/**\n * Ends span\n * @param reply - reply function\n * @param err - error\n */\nexport function endSpan(reply: PluginFastifyReply, err?: any) {\n  const spans = reply[spanRequestSymbol] || [];\n  // there is no active span, or it has already ended\n  if (!spans.length) {\n    return;\n  }\n  // biome-ignore lint/complexity/noForEach: <explanation>\n  spans.forEach((span: Span) => {\n    if (err) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n      span.recordException(err);\n    }\n    span.end();\n  });\n  delete reply[spanRequestSymbol];\n}\n\n// @TODO after approve add this to instrumentation package and replace usage\n// when it will be released\n\n/**\n * This function handles the missing case from instrumentation package when\n * execute can either return a promise or void. And using async is not an\n * option as it is producing unwanted side effects.\n * @param execute - function to be executed\n * @param onFinish - function called when function executed\n * @param preventThrowingError - prevent to throw error when execute\n * function fails\n */\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): Promise<T>;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): T;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T | Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): T | Promise<T> | undefined {\n  let error: unknown;\n  let result: T | Promise<T> | undefined = undefined;\n  try {\n    result = execute();\n\n    if (isPromise(result)) {\n      result.then(\n        res => onFinish(undefined, res),\n        err => onFinish(err),\n      );\n    }\n  } catch (e) {\n    error = e;\n  } finally {\n    if (!isPromise(result)) {\n      onFinish(error, result);\n      if (error && !preventThrowingError) {\n        // eslint-disable-next-line no-unsafe-finally\n        throw error;\n      }\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result;\n  }\n}\n\nfunction isPromise<T>(val: T | Promise<T>): val is Promise<T> {\n  return (\n    (typeof val === 'object' && val && typeof Object.getOwnPropertyDescriptor(val, 'then')?.value === 'function') ||\n    false\n  );\n}\n"], "names": ["spanRequestSymbol", "SpanStatusCode"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS;AACzB,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,cAAc,GAAe,EAAE;AACjC,EAAE;AACF,EAAE,MAAM,IAAA,GAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,cAAA,EAAgB,CAAC;;AAEzE,EAAE,MAAM,KAAK,GAAW,KAAK,CAACA,2BAAiB,CAAA,IAAK,EAAE;AACtD,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAElB;AACA,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAEA,2BAAiB,EAAE;AAClD,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,KAAK,EAAE,KAAK;AAChB,GAAG,CAAC;;AAEJ,EAAE,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,KAAK,EAAsB,GAAG,EAAQ;AAC9D,EAAE,MAAM,QAAQ,KAAK,CAACA,2BAAiB,CAAA,IAAK,EAAE;AAC9C;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACrB,IAAI;AACJ;AACA;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAW;AAChC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,IAAI,EAAEC,kBAAc,CAAC,KAAK;AAClC,QAAQ,OAAO,EAAE,GAAG,CAAC,OAAO;AAC5B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;AAC/B;AACA,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,OAAO,KAAK,CAACD,2BAAiB,CAAC;AACjC;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAWO,SAAS,kCAAkC;AAClD,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,oBAAoB;AACtB,EAA8B;AAC9B,EAAE,IAAI,KAAK;AACX,EAAE,IAAI,MAAM,GAA+B,SAAS;AACpD,EAAE,IAAI;AACN,IAAI,MAAO,GAAE,OAAO,EAAE;;AAEtB,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AAC3B,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,OAAO,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC;AACvC,QAAQ,GAAI,IAAG,QAAQ,CAAC,GAAG,CAAC;AAC5B,OAAO;AACP;AACA,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,KAAA,GAAQ,CAAC;AACb,YAAY;AACZ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC5B,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;AAC7B,MAAM,IAAI,KAAA,IAAS,IAAqB,EAAE;AAC1C;AACA,QAAQ,MAAM,KAAK;AACnB;AACA;AACA;AACA,IAAI,OAAO,MAAM;AACjB;AACA;;AAEA,SAAS,SAAS,CAAI,GAAG,EAAqC;AAC9D,EAAE;AACF,IAAI,CAAC,OAAO,GAAI,KAAI,YAAY,GAAA,IAAO,OAAO,MAAM,CAAC,wBAAwB,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,KAAA,KAAU,UAAU;AAChH,IAAI;AACJ;AACA;;;;;;"}