# ============================================================================
# 🔧 CONFIGURAÇÃO DE AMBIENTE - EXCEL COPILOT (DESENVOLVIMENTO LOCAL)
# ============================================================================
# Arquivo de configuração para desenvolvimento local do Excel Copilot
# ⚠️ IMPORTANTE: Este arquivo contém credenciais reais - NÃO commite no Git
#
# 📋 STATUS: 🎉 100% CONFIGURADO E FUNCIONANDO!
# - ✅ NextAuth secret gerado e configurado
# - ✅ Todas as credenciais configuradas
# - ✅ Stripe webhooks funcionando
# - ✅ MCPs ativas (5/5)
# - ✅ PRONTO PARA npm run dev!
# ============================================================================

# ============================================================================
# CONFIGURAÇÕES BÁSICAS
# ============================================================================
NODE_ENV="development"
APP_NAME="Excel Copilot"
APP_VERSION="1.0.0"
APP_URL="http://localhost:3000"

# ============================================================================
# AUTENTICAÇÃO - NEXTAUTH (OBRIGATÓRIAS)
# ============================================================================
# Gerar com: openssl rand -base64 32
AUTH_NEXTAUTH_SECRET="tfvUcb2ckImUh+ncBrMb/mmgVM+fTB0M82vEsKqdQtw="
AUTH_NEXTAUTH_URL="http://localhost:3000"
AUTH_SKIP_PROVIDERS="false"

# OAuth Providers - CONFIGURAÇÃO PARA DESENVOLVIMENTO LOCAL
# ✅ CREDENCIAIS ESPECÍFICAS DE DESENVOLVIMENTO CONFIGURADAS
AUTH_GOOGLE_CLIENT_ID="217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com"
AUTH_GOOGLE_CLIENT_SECRET="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"
AUTH_GITHUB_CLIENT_ID="********************"          # Excel Copilot Dev
AUTH_GITHUB_CLIENT_SECRET="71713b5d91b5d5948579254fbf472042ee3ffa96"

# 📋 APPS OAUTH CONFIGURADAS:
# 🚀 Produção: "Excel Copilot" (Ov23likI0qAu9fFNk7My)
# 🔧 Desenvolvimento: "Excel Copilot Dev" (********************)

# ============================================================================
# BANCO DE DADOS - DESENVOLVIMENTO
# ============================================================================
# ✅ OPÇÃO 1: Use o Supabase de produção (recomendado para desenvolvimento)
DB_DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"
DB_DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"

# Supabase Configuration
SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
NEXT_PUBLIC_SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0NTYxNCwiZXhwIjoyMDYyMTIxNjE0fQ.hHguPBu7OV6CJBSmwe3r7JwG1Ob__NWt-dWAnRsofP8"

# ⚠️ OPÇÃO 2: Banco local PostgreSQL (se preferir isolamento total)
# DB_DATABASE_URL="postgresql://postgres:password@localhost:5432/excel_copilot_dev"
# DB_DIRECT_URL="postgresql://postgres:password@localhost:5432/excel_copilot_dev"

# ============================================================================
# PAGAMENTOS - STRIPE (TESTE)
# ============================================================================
# ⚠️ IMPORTANTE: Use sempre chaves de TESTE em desenvolvimento
# Obtenha em: https://dashboard.stripe.com/test/apikeys
STRIPE_SECRET_KEY="sk_test_51RGJ6nRrKLXtzZkMsTL0PXbDQ5sFcXXMaaAqWNRZz8vs2wqB22PijCYJpjyZuOu2iZUpRIgIa1WcDm7cR5zbfZow00I0zvl4lN"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51RGJ6nRrKLXtzZkM0EqlkOaZIpDNCLiGwLAHXr1YOmHWGIZB5RxMImDy2bBVyErg7PiQ2T6vqS1pFSK6O4nSEdQJ00Tz72hUX7"
STRIPE_WEBHOOK_SECRET="whsec_b47aa3b4ea41e430460def4eeb730a8e9e999d29b2e567377e9bd0887715638e"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_1RWHbARrKLXtzZkME498Zuab"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_1RWHckRrKLXtzZkMLLn1vFvh"

# ============================================================================
# INTELIGÊNCIA ARTIFICIAL - VERTEX AI
# ============================================================================
# 🚀 PRODUÇÃO: IA real habilitada com Vertex AI
AI_ENABLED="true"
AI_USE_MOCK="false"
AI_VERTEX_PROJECT_ID="excel-copilot"
AI_VERTEX_LOCATION="us-central1"
AI_VERTEX_MODEL="gemini-2.0-flash-001"

# ⚠️ Para voltar aos mocks (sem custos):
# AI_USE_MOCK="true"

# ============================================================================
# CACHE E REDIS - CONFIGURAÇÃO COMPLETA
# ============================================================================
# ✅ OPÇÃO 1: Use o Redis de produção (recomendado para desenvolvimento)
UPSTASH_REDIS_REST_URL="https://cunning-pup-26344.upstash.io"
UPSTASH_REDIS_REST_TOKEN="AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA"

# Configurações Redis nativo para cache e filas
REDIS_URL="redis://default:<EMAIL>:6379"
REDIS_HOST="cunning-pup-26344.upstash.io"
REDIS_PORT="6379"
REDIS_PASSWORD="AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA"

# ⚠️ OPÇÃO 2: Redis local para desenvolvimento isolado
# REDIS_URL="redis://localhost:6379"
# REDIS_HOST="localhost"
# REDIS_PORT="6379"
# REDIS_PASSWORD=""

# Configurações de Filas (Bull/BullMQ)
QUEUE_REDIS_DB="1"  # Database 1 para filas (0 para cache)
QUEUE_CONCURRENCY="3"  # Máximo de jobs simultâneos
QUEUE_MAX_ATTEMPTS="3"  # Tentativas máximas por job

# Configurações de cache otimizadas para desenvolvimento
AI_CACHE_SIZE="100"
AI_CACHE_TTL="3600"
EXCEL_CACHE_SIZE="50"
EXCEL_CACHE_TTL="1800"
CACHE_DEFAULT_TTL="1800"

# ============================================================================
# SEGURANÇA E VALIDAÇÃO
# ============================================================================
DEV_FORCE_PRODUCTION="false"
BUILD_MODE="local"

# ============================================================================
# TESTES E2E - PLAYWRIGHT
# ============================================================================
# Configurações para testes automatizados
PLAYWRIGHT_BASE_URL="http://localhost:3000"
TEST_USER_EMAIL="<EMAIL>"
TEST_USER_PASSWORD="password123"

# Configurações opcionais para testes com OAuth real
# TEST_GOOGLE_EMAIL=""
# TEST_GOOGLE_PASSWORD=""

# ============================================================================
# INTEGRAÇÕES MCP - NOMENCLATURA PADRONIZADA
# ============================================================================
# ✅ OPÇÃO 1: Use as integrações de produção (recomendado para desenvolvimento)
# Vercel MCP
MCP_VERCEL_TOKEN="************************"
MCP_VERCEL_PROJECT_ID="prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg"
MCP_VERCEL_TEAM_ID="team_BLCIn3CF09teqBeBn8u0fLqp"

# Linear MCP
MCP_LINEAR_API_KEY="************************************************"

# GitHub MCP
MCP_GITHUB_TOKEN="****************************************"

# ⚠️ OPÇÃO 2: Desabilitar MCPs para desenvolvimento simples
# MCP_VERCEL_TOKEN=""
# MCP_VERCEL_PROJECT_ID=""
# MCP_VERCEL_TEAM_ID=""
# MCP_LINEAR_API_KEY=""
# MCP_GITHUB_TOKEN=""

# ============================================================================
# MONITORAMENTO E OBSERVABILIDADE
# ============================================================================
# ✅ OPÇÃO 1: Use o Sentry de produção (recomendado para capturar erros de dev)
SENTRY_DSN="https://<EMAIL>/4509435346223104"
SENTRY_ORG="ngbprojectsentry"
SENTRY_PROJECT="excel-copilot"

# ⚠️ OPÇÃO 2: Desabilitar monitoramento para desenvolvimento simples
# SENTRY_DSN=""
# SENTRY_ORG=""
# SENTRY_PROJECT=""

# Vercel (para desenvolvimento local)
VERCEL_BUILD_DATABASE_MIGRATION="false"

# ============================================================================
# 📋 COMO USAR ESTE ARQUIVO
# ============================================================================
#
# 🚀 DESENVOLVIMENTO IMEDIATO:
# ✅ TUDO CONFIGURADO! Pronto para usar:
# 1. Execute: npm run dev
# 2. Acesse: http://localhost:3000
# 3. ✅ Todas as funcionalidades funcionando!
#
# ✅ CONFIGURAÇÃO COMPLETA:
# - ✅ Banco de dados: Supabase de produção (seguro para dev)
# - ✅ Autenticação: Google + GitHub OAuth funcionando
# - ✅ Cache: Redis de produção + Sistema de filas Bull
# - ✅ MCPs: Todas as 5 integrações ativas
# - ✅ Monitoramento: Sentry capturando erros
# - ✅ Stripe: Chaves de TESTE configuradas + Webhooks
# - ✅ IA: Vertex AI REAL habilitada (Gemini 2.0 Flash)
# - ✅ Filas: Processamento assíncrono de IA configurado
# - ✅ Testes: Playwright E2E configurado
#
# 🔧 PERSONALIZAÇÃO:
# - Para isolar completamente: comente as credenciais de produção
# - Para economizar créditos IA: mude AI_USE_MOCK="true"
# - Para testar pagamentos: Stripe TEST keys já configuradas
#
# 📖 DOCUMENTAÇÃO COMPLETA:
# - Configuração: CONFIGURACAO_AMBIENTE.md
# - MCPs: docs/MCP_*.md
# - Troubleshooting: README.md
#
# 🔧 STRIPE WEBHOOKS - CONFIGURADO E FUNCIONANDO:
# ✅ Stripe CLI já configurado no projeto
# ✅ Webhook secret já configurado abaixo
#
# Para testar webhooks localmente:
# 1. Execute: ./stripe-cli/stripe.exe listen --forward-to localhost:3000/api/webhooks/stripe
# 2. O webhook secret já está configurado neste arquivo!
# 3. Teste pagamentos e veja os eventos chegando em tempo real
#
# ⚡ WEBHOOK SECRET ATIVO:
# whsec_b47aa3b4ea41e430460def4eeb730a8e9e999d29b2e567377e9bd0887715638e
# (Configurado e pronto para uso - válido para desenvolvimento local)
# ============================================================================

# ============================================================================
# VARIÁVEIS COMPATÍVEIS COM NEXT.JS (MAPEAMENTO PARA SISTEMA LEGADO)
# ============================================================================
# Estas variáveis são necessárias para o build funcionar localmente
NEXTAUTH_SECRET="tfvUcb2ckImUh+ncBrMb/mmgVM+fTB0M82vEsKqdQtw="
NEXTAUTH_URL="http://localhost:3000"
DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"
DB_DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"
DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"
CACHE_SECRET=d1bfc82ccb50ac0d97906034eb28ecf6607827897c4c49444f44c2026b2d319d
