import { Event, EventHint } from '../event';
import { Primitive } from '../misc';
import { User } from '../user';
/**
 * Crash report feedback object
 */
export interface UserFeedback {
    event_id: string;
    email: User['email'];
    name: string;
    comments: string;
}
interface FeedbackContext extends Record<string, unknown> {
    message: string;
    contact_email?: string;
    name?: string;
    replay_id?: string;
    url?: string;
    associated_event_id?: string;
    source?: string;
}
/**
 * NOTE: These types are still considered Alpha and subject to change.
 * @hidden
 */
export interface FeedbackEvent extends Event {
    type: 'feedback';
    contexts: Event['contexts'] & {
        feedback: FeedbackContext;
    };
}
export interface SendFeedbackParams {
    message: string;
    name?: string;
    email?: string;
    url?: string;
    source?: string;
    associatedEventId?: string;
    /**
     * Set an object that will be merged sent as tags data with the event.
     */
    tags?: {
        [key: string]: Primitive;
    };
}
export type SendFeedback = (params: SendFeedbackParams, hint?: EventHint & {
    includeReplay?: boolean;
}) => Promise<string>;
export {};
//# sourceMappingURL=sendFeedback.d.ts.map
