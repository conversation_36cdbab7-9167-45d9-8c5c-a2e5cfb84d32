import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/server/auth/options';
import { prisma } from '@/server/db/client';
import { createTemplateCategorySchema } from '@/schemas/template';
import { ApiResponse } from '@/lib/api-response';

/**
 * GET /api/templates/categories
 * Lista todas as categorias de templates ativas
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeCount = searchParams.get('includeCount') === 'true';

    const categories = await prisma.templateCategory.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' },
      ],
      ...(includeCount && {
        include: {
          _count: {
            select: {
              templates: {
                where: {
                  isActive: true,
                  isPublic: true,
                },
              },
            },
          },
        },
      }),
    });

    const formattedCategories = categories.map((category: any) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      color: category.color,
      sortOrder: category.sortOrder,
      ...(includeCount && {
        templateCount: category._count?.templates || 0,
      }),
    }));

    return ApiResponse.success({
      categories: formattedCategories,
    });
  } catch (error) {
    console.error('Erro ao buscar categorias de templates:', error);
    return ApiResponse.error(
      error instanceof Error ? error.message : 'Erro interno do servidor',
      500
    );
  }
}

/**
 * POST /api/templates/categories
 * Cria uma nova categoria de template (requer autenticação de admin)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return ApiResponse.error('Não autorizado', 401);
    }

    // TODO: Verificar se o usuário é admin
    // Por enquanto, qualquer usuário autenticado pode criar categorias
    // Em produção, adicionar verificação de role de admin

    const body = await request.json();
    const categoryData = createTemplateCategorySchema.parse(body);

    // Verificar se já existe uma categoria com o mesmo nome ou slug
    const existingCategory = await prisma.templateCategory.findFirst({
      where: {
        OR: [
          { name: categoryData.name },
          { slug: categoryData.slug },
        ],
      },
    });

    if (existingCategory) {
      return ApiResponse.error(
        'Já existe uma categoria com este nome ou slug',
        400
      );
    }

    // Criar categoria
    const category = await prisma.templateCategory.create({
      data: {
        name: categoryData.name,
        slug: categoryData.slug,
        description: categoryData.description || null,
        icon: categoryData.icon || null,
        color: categoryData.color || null,
        sortOrder: categoryData.sortOrder,
      },
    });

    return ApiResponse.success(category, 201);
  } catch (error) {
    console.error('Erro ao criar categoria de template:', error);
    return ApiResponse.error(
      error instanceof Error ? error.message : 'Erro interno do servidor',
      500
    );
  }
}
