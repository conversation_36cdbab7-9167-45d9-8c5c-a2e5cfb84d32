import { TransactionEvent } from '../types-hoist/event';
import { SpanJSON } from '../types-hoist/span';
/**
 * Converts a transaction event to a span JSON object.
 */
export declare function convertTransactionEventToSpanJson(event: TransactionEvent): SpanJSON;
/**
 * Converts a span JSON object to a transaction event.
 */
export declare function convertSpanJsonToTransactionEvent(span: SpanJSON): TransactionEvent;
//# sourceMappingURL=transactionEvent.d.ts.map
