import type { Measurements, MeasurementUnit } from '../types-hoist/measurement';
import type { TimedEvent } from '../types-hoist/timedEvent';
/**
 * Adds a measurement to the active transaction on the current global scope. You can optionally pass in a different span
 * as the 4th parameter.
 */
export declare function setMeasurement(name: string, value: number, unit: MeasurementUnit, activeSpan?: import("..").Span | undefined): void;
/**
 * Convert timed events to measurements.
 */
export declare function timedEventsToMeasurements(events: TimedEvent[]): Measurements | undefined;
//# sourceMappingURL=measurement.d.ts.map