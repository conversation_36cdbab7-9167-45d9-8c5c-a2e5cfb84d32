import { useState, useEffect, createContext, useContext, ReactNode } from 'react';

import { Locale, Messages, getMessages, i18nConfig } from '@/config/i18n';

// Contexto para prover o i18n em toda a aplicação
interface I18nContextValue {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: Messages;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextValue>({
  locale: i18nConfig.defaultLocale,
  setLocale: () => {},
  t: getMessages(i18nConfig.defaultLocale),
  isLoading: false,
});

// Provider do i18n para envolver a aplicação
export function I18nProvider({ children }: { children: ReactNode }): JSX.Element {
  // Estado do locale com suporte a múltiplos idiomas
  const [locale, setLocaleState] = useState<Locale>(i18nConfig.defaultLocale);
  const [messages, setMessages] = useState<Messages>(getMessages(i18nConfig.defaultLocale));
  const [isLoading, setIsLoading] = useState(false);

  // Função para alterar o locale com persistência
  const setLocale = (newLocale: Locale) => {
    if (!i18nConfig.locales.includes(newLocale)) {
      console.warn(`Locale ${newLocale} não é suportado. Usando ${i18nConfig.defaultLocale}.`);
      return;
    }

    setIsLoading(true);

    try {
      // Atualizar estado
      setLocaleState(newLocale);

      // Persistir no localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', newLocale);
      }

      // Atualizar mensagens
      setMessages(getMessages(newLocale));

      // Atualizar atributo lang do documento
      document.documentElement.lang = newLocale;

    } catch (error) {
      console.error('Erro ao alterar locale:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Efeito para inicializar o locale baseado no localStorage ou browser
  useEffect(() => {
    setIsLoading(true);

    try {
      let initialLocale = i18nConfig.defaultLocale;

      // Verificar localStorage primeiro
      if (typeof window !== 'undefined') {
        const savedLocale = localStorage.getItem('locale') as Locale;
        if (savedLocale && i18nConfig.locales.includes(savedLocale)) {
          initialLocale = savedLocale;
        } else {
          // Detectar idioma do browser como fallback
          const browserLocale = navigator.language;
          const matchedLocale = i18nConfig.locales.find(locale => {
            const localePrefix = locale.split('-')[0];
            return localePrefix && browserLocale.startsWith(localePrefix);
          });
          if (matchedLocale) {
            initialLocale = matchedLocale;
          }
        }
      }

      // Configurar locale inicial
      setLocaleState(initialLocale);
      document.documentElement.lang = initialLocale;
      setMessages(getMessages(initialLocale));

    } catch (error) {
      console.error('Erro ao inicializar i18n:', error);
      // Fallback para locale padrão
      setLocaleState(i18nConfig.defaultLocale);
      setMessages(getMessages(i18nConfig.defaultLocale));
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <I18nContext.Provider value={{ locale, setLocale, t: messages, isLoading }}>
      {children}
    </I18nContext.Provider>
  );
}

// Hook para usar o contexto i18n
export function useI18n(): I18nContextValue {
  return useContext(I18nContext);
}
