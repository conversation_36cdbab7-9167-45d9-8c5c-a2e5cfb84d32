import { Locale, i18nConfig } from '@/config/i18n';

// Interface para informações de detecção
export interface LocaleDetectionInfo {
  detectedLocale: Locale;
  confidence: number; // 0-1, onde 1 é máxima confiança
  source: 'browser' | 'ip' | 'domain' | 'localStorage' | 'default';
  browserLanguages: string[];
  userAgent?: string;
}

/**
 * Detecta o locale preferido do usuário baseado em múltiplas fontes
 * @returns Informações de detecção do locale
 */
export function detectUserLocale(): LocaleDetectionInfo {
  const detectionInfo: LocaleDetectionInfo = {
    detectedLocale: i18nConfig.defaultLocale,
    confidence: 0,
    source: 'default',
    browserLanguages: [],
  };

  // 1. Verificar localStorage primeiro (maior prioridade)
  if (typeof window !== 'undefined') {
    const savedLocale = localStorage.getItem('locale') as Locale;
    if (savedLocale && i18nConfig.locales.includes(savedLocale)) {
      return {
        ...detectionInfo,
        detectedLocale: savedLocale,
        confidence: 1.0,
        source: 'localStorage',
      };
    }
  }

  // 2. Detectar baseado no domínio
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const domainLocale = detectLocaleFromDomain(hostname);
    if (domainLocale) {
      return {
        ...detectionInfo,
        detectedLocale: domainLocale,
        confidence: 0.9,
        source: 'domain',
      };
    }
  }

  // 3. Detectar baseado no browser
  if (typeof navigator !== 'undefined') {
    const browserDetection = detectLocaleFromBrowser();
    if (browserDetection.locale) {
      return {
        ...detectionInfo,
        detectedLocale: browserDetection.locale,
        confidence: browserDetection.confidence,
        source: 'browser',
        browserLanguages: browserDetection.languages,
        userAgent: navigator.userAgent,
      };
    }
  }

  // 4. Fallback para locale padrão
  return detectionInfo;
}

/**
 * Detecta locale baseado no domínio
 * @param hostname Nome do host
 * @returns Locale detectado ou null
 */
function detectLocaleFromDomain(hostname: string): Locale | null {
  // Verificar domínios configurados
  for (const domain of i18nConfig.domains || []) {
    if (hostname.includes(domain.domain)) {
      return domain.defaultLocale;
    }
  }

  // Verificar TLD (Top Level Domain)
  if (hostname.endsWith('.br') || hostname.includes('.com.br')) {
    return 'pt-BR';
  }

  if (hostname.endsWith('.com') && !hostname.includes('.com.br')) {
    return 'en-US';
  }

  return null;
}

/**
 * Detecta locale baseado nas preferências do browser
 * @returns Informações de detecção do browser
 */
function detectLocaleFromBrowser(): {
  locale: Locale | null;
  confidence: number;
  languages: string[];
} {
  const languages = Array.from(navigator.languages || [navigator.language]);
  
  // Mapear idiomas do browser para locales suportados
  const localeMapping: Record<string, { locale: Locale; confidence: number }> = {
    'pt': { locale: 'pt-BR', confidence: 0.8 },
    'pt-BR': { locale: 'pt-BR', confidence: 1.0 },
    'pt-PT': { locale: 'pt-BR', confidence: 0.7 }, // Português de Portugal -> Brasil
    'en': { locale: 'en-US', confidence: 0.8 },
    'en-US': { locale: 'en-US', confidence: 1.0 },
    'en-GB': { locale: 'en-US', confidence: 0.7 }, // Inglês britânico -> americano
    'en-CA': { locale: 'en-US', confidence: 0.7 }, // Inglês canadense -> americano
  };

  // Procurar o melhor match
  for (const language of languages) {
    // Verificar match exato
    if (localeMapping[language]) {
      return {
        locale: localeMapping[language].locale,
        confidence: localeMapping[language].confidence,
        languages,
      };
    }

    // Verificar match por código de idioma (ex: 'pt' de 'pt-BR')
    const languageCode = language.split('-')[0];
    if (languageCode && localeMapping[languageCode]) {
      return {
        locale: localeMapping[languageCode].locale,
        confidence: localeMapping[languageCode].confidence * 0.8, // Reduzir confiança
        languages,
      };
    }
  }

  return {
    locale: null,
    confidence: 0,
    languages,
  };
}

/**
 * Detecta locale baseado no IP (simulado - em produção usaria serviço real)
 * @returns Promise com locale detectado
 */
export async function detectLocaleFromIP(): Promise<Locale | null> {
  try {
    // Em produção, isso faria uma chamada para um serviço de geolocalização
    // Por exemplo: ipapi.co, ipinfo.io, etc.
    
    // Simulação baseada no timezone
    if (typeof Intl !== 'undefined') {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      
      // Timezones brasileiros
      const brazilianTimezones = [
        'America/Sao_Paulo',
        'America/Manaus',
        'America/Fortaleza',
        'America/Recife',
        'America/Bahia',
        'America/Cuiaba',
        'America/Porto_Velho',
        'America/Boa_Vista',
        'America/Rio_Branco',
      ];
      
      if (brazilianTimezones.includes(timezone)) {
        return 'pt-BR';
      }
      
      // Timezones americanos/ingleses
      const englishTimezones = [
        'America/New_York',
        'America/Los_Angeles',
        'America/Chicago',
        'America/Denver',
        'Europe/London',
        'America/Toronto',
      ];
      
      if (englishTimezones.includes(timezone)) {
        return 'en-US';
      }
    }
    
    return null;
  } catch (error) {
    console.warn('Erro na detecção de locale por IP:', error);
    return null;
  }
}

/**
 * Aplica o locale detectado com validação
 * @param detectionInfo Informações de detecção
 * @returns Locale aplicado
 */
export function applyDetectedLocale(detectionInfo: LocaleDetectionInfo): Locale {
  // Validar se o locale é suportado
  if (!i18nConfig.locales.includes(detectionInfo.detectedLocale)) {
    console.warn(
      `Locale detectado ${detectionInfo.detectedLocale} não é suportado. Usando ${i18nConfig.defaultLocale}.`
    );
    return i18nConfig.defaultLocale;
  }

  // Aplicar locale se a confiança for suficiente
  if (detectionInfo.confidence >= 0.5) {
    // Salvar no localStorage para próximas visitas
    if (typeof window !== 'undefined' && detectionInfo.source !== 'localStorage') {
      localStorage.setItem('locale', detectionInfo.detectedLocale);
    }

    // Atualizar atributo lang do documento
    if (typeof document !== 'undefined') {
      document.documentElement.lang = detectionInfo.detectedLocale;
    }

    return detectionInfo.detectedLocale;
  }

  // Fallback para locale padrão
  return i18nConfig.defaultLocale;
}

/**
 * Hook para usar detecção automática de locale
 * @returns Informações de detecção e locale aplicado
 */
export function useLocaleDetection() {
  const [detectionInfo, setDetectionInfo] = useState<LocaleDetectionInfo | null>(null);
  const [appliedLocale, setAppliedLocale] = useState<Locale>(i18nConfig.defaultLocale);

  useEffect(() => {
    const detectAndApply = async () => {
      // Detecção básica
      const basicDetection = detectUserLocale();
      
      // Tentar detecção por IP se a confiança for baixa
      if (basicDetection.confidence < 0.7) {
        const ipLocale = await detectLocaleFromIP();
        if (ipLocale) {
          basicDetection.detectedLocale = ipLocale;
          basicDetection.confidence = 0.6;
          basicDetection.source = 'ip';
        }
      }
      
      setDetectionInfo(basicDetection);
      
      const finalLocale = applyDetectedLocale(basicDetection);
      setAppliedLocale(finalLocale);
    };

    detectAndApply();
  }, []);

  return {
    detectionInfo,
    appliedLocale,
    isDetecting: detectionInfo === null,
  };
}

// Importações necessárias para o hook
import { useState, useEffect } from 'react';
