{"version": 3, "file": "utils.js", "sources": ["../../../../../src/integrations/tracing/express-v5/utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-assertion */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Attributes } from '@opentelemetry/api';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport type { ExpressLayer, PatchedRequest } from './internal-types';\nimport { _LAYERS_STORE_PROPERTY } from './internal-types';\nimport type { ExpressInstrumentationConfig, IgnoreMatcher, LayerPathSegment } from './types';\n\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nexport const storeLayerPath = (request: PatchedRequest, value?: string): void => {\n  if (Array.isArray(request[_LAYERS_STORE_PROPERTY]) === false) {\n    Object.defineProperty(request, _LAYERS_STORE_PROPERTY, {\n      enumerable: false,\n      value: [],\n    });\n  }\n  if (value === undefined) return;\n  (request[_LAYERS_STORE_PROPERTY] as string[]).push(value);\n};\n\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nexport const getRouterPath = (path: string, layer: ExpressLayer): string => {\n  const stackLayer = layer.handle?.stack?.[0];\n\n  if (stackLayer?.route?.path) {\n    return `${path}${stackLayer.route.path}`;\n  }\n\n  if (stackLayer?.handle?.stack) {\n    return getRouterPath(path, stackLayer);\n  }\n\n  return path;\n};\n\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nexport const getLayerMetadata = (\n  route: string,\n  layer: ExpressLayer,\n  layerPath?: string,\n): {\n  attributes: Attributes;\n  name: string;\n} => {\n  if (layer.name === 'router') {\n    const maybeRouterPath = getRouterPath('', layer);\n    const extractedRouterPath = maybeRouterPath ? maybeRouterPath : layerPath || route || '/';\n\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.ROUTER,\n      },\n      name: `router - ${extractedRouterPath}`,\n    };\n  } else if (layer.name === 'bound dispatch' || layer.name === 'handle') {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: (route || layerPath) ?? 'request handler',\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.REQUEST_HANDLER,\n      },\n      name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n    };\n  } else {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layer.name,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.MIDDLEWARE,\n      },\n      name: `middleware - ${layer.name}`,\n    };\n  }\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (constant: string, pattern: IgnoreMatcher): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isLayerIgnored = (\n  name: string,\n  type: ExpressLayerType,\n  config?: ExpressInstrumentationConfig,\n): boolean => {\n  if (Array.isArray(config?.ignoreLayersType) && config?.ignoreLayersType?.includes(type)) {\n    return true;\n  }\n  if (Array.isArray(config?.ignoreLayers) === false) return false;\n  try {\n    for (const pattern of config!.ignoreLayers!) {\n      if (satisfiesPattern(name, pattern)) {\n        return true;\n      }\n    }\n  } catch (e) {\n    /* catch block */\n  }\n\n  return false;\n};\n\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nexport const asErrorAndMessage = (error: unknown): [error: string | Error, message: string] =>\n  error instanceof Error ? [error, error.message] : [String(error), String(error)];\n\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nexport const getLayerPath = (args: [LayerPathSegment | LayerPathSegment[], ...unknown[]]): string | undefined => {\n  const firstArg = args[0];\n\n  if (Array.isArray(firstArg)) {\n    return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n  }\n\n  return extractLayerPathSegment(firstArg);\n};\n\nconst extractLayerPathSegment = (arg: LayerPathSegment) => {\n  if (typeof arg === 'string') {\n    return arg;\n  }\n\n  if (arg instanceof RegExp || typeof arg === 'number') {\n    return arg.toString();\n  }\n\n  return;\n};\n"], "names": [], "mappings": ";;;;AA2BA;AACA;AACA;AACA;AACA;MACa,cAAe,GAAE,CAAC,OAAO,EAAkB,KAAK,KAAoB;AACjF,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAE,KAAI,KAAK,EAAE;AAChE,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,sBAAsB,EAAE;AAC3D,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,KAAK,EAAE,EAAE;AACf,KAAK,CAAC;AACN;AACA,EAAE,IAAI,KAAA,KAAU,SAAS,EAAE;AAC3B,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAA,GAAe,IAAI,CAAC,KAAK,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;MACa,aAAc,GAAE,CAAC,IAAI,EAAU,KAAK,KAA2B;AAC5E,EAAE,MAAM,UAAW,GAAE,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC;;AAE7C,EAAE,IAAI,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;AAC/B,IAAI,OAAO,CAAC,EAAA,IAAA,CAAA,EAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,UAAA,EAAA,MAAA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,aAAA,CAAA,IAAA,EAAA,UAAA,CAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,gBAAA,GAAA;AACA,EAAA,KAAA;AACA,EAAA,KAAA;AACA,EAAA,SAAA;;AAIA,KAAA;AACA,EAAA,IAAA,KAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,IAAA,MAAA,eAAA,GAAA,aAAA,CAAA,EAAA,EAAA,KAAA,CAAA;AACA,IAAA,MAAA,mBAAA,GAAA,eAAA,GAAA,eAAA,GAAA,SAAA,IAAA,KAAA,IAAA,GAAA;;AAEA,IAAA,OAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,mBAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,gBAAA,CAAA,MAAA;AACA,OAAA;AACA,MAAA,IAAA,EAAA,CAAA,SAAA,EAAA,mBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,MAAA,IAAA,KAAA,CAAA,IAAA,KAAA,gBAAA,IAAA,KAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,CAAA,KAAA,IAAA,SAAA,KAAA,iBAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,gBAAA,CAAA,eAAA;AACA,OAAA;AACA,MAAA,IAAA,EAAA,CAAA,eAAA,EAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,EAAA,KAAA,IAAA,SAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,MAAA;AACA,IAAA,OAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,KAAA,CAAA,IAAA;AACA,QAAA,CAAA,cAAA,CAAA,YAAA,GAAA,gBAAA,CAAA,UAAA;AACA,OAAA;AACA,MAAA,IAAA,EAAA,CAAA,aAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,gBAAA,GAAA,CAAA,QAAA,EAAA,OAAA,KAAA;AACA,EAAA,IAAA,OAAA,OAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,OAAA,KAAA,QAAA;AACA,GAAA,MAAA,IAAA,OAAA,YAAA,MAAA,EAAA;AACA,IAAA,OAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA;AACA,GAAA,MAAA,IAAA,OAAA,OAAA,KAAA,UAAA,EAAA;AACA,IAAA,OAAA,OAAA,CAAA,QAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA,IAAA,SAAA,CAAA,oCAAA,CAAA;AACA;AACA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,cAAA,GAAA;AACA,EAAA,IAAA;AACA,EAAA,IAAA;AACA,EAAA,MAAA;AACA,KAAA;AACA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,IAAA,MAAA,EAAA,gBAAA,EAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,EAAA,YAAA,CAAA,KAAA,KAAA,EAAA,OAAA,KAAA;AACA,EAAA,IAAA;AACA,IAAA,KAAA,MAAA,OAAA,IAAA,MAAA,CAAA,YAAA,EAAA;AACA,MAAA,IAAA,gBAAA,CAAA,IAAA,EAAA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,IAAA;AACA;AACA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA;;AAEA,EAAA,OAAA,KAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,iBAAA,GAAA,CAAA,KAAA;AACA,EAAA,KAAA,YAAA,KAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,MAAA,CAAA,KAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AACA,EAAA,MAAA,QAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AAEA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AACA,IAAA,OAAA,QAAA,CAAA,GAAA,CAAA,GAAA,IAAA,uBAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA;;AAEA,EAAA,OAAA,uBAAA,CAAA,QAAA,CAAA;AACA;;AAEA,MAAA,uBAAA,GAAA,CAAA,GAAA,KAAA;AACA,EAAA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,GAAA;AACA;;AAEA,EAAA,IAAA,GAAA,YAAA,MAAA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,GAAA,CAAA,QAAA,EAAA;AACA;;AAEA,EAAA;AACA,CAAA;;;;"}