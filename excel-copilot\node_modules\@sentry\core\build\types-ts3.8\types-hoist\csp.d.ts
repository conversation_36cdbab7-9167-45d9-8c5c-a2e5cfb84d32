export interface LegacyCSPReport {
    readonly 'csp-report': {
        readonly 'document-uri'?: string;
        readonly referrer?: string;
        readonly 'blocked-uri'?: string;
        readonly 'effective-directive'?: string;
        readonly 'violated-directive'?: string;
        readonly 'original-policy'?: string;
        readonly disposition: 'enforce' | 'report' | 'reporting';
        readonly 'status-code'?: number;
        readonly status?: string;
        readonly 'script-sample'?: string;
        readonly sample?: string;
    };
}
//# sourceMappingURL=csp.d.ts.map
