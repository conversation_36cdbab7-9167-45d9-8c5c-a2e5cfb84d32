'use client';

import { useState, useEffect } from 'react';
import { Globe, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Locale, i18nConfig } from '@/config/i18n';
import { useI18n } from '@/hooks/useI18n';

// Configuração dos idiomas disponíveis
const LOCALE_CONFIG = {
  'pt-BR': {
    name: 'Português (Brasil)',
    flag: '🇧🇷',
    shortName: 'PT',
  },
  'en-US': {
    name: 'English (United States)',
    flag: '🇺🇸',
    shortName: 'EN',
  },
} as const;

/**
 * Componente de seleção de idioma com dropdown
 * Permite alternar entre português e inglês com persistência
 */
export function LocaleSwitcher() {
  const { locale, setLocale, isLoading } = useI18n();
  const [mounted, setMounted] = useState(false);

  // Evitar hidration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button variant="ghost" size="sm" disabled>
        <Globe className="h-4 w-4" />
      </Button>
    );
  }

  const currentLocaleConfig = LOCALE_CONFIG[locale as keyof typeof LOCALE_CONFIG];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={isLoading}
          className="gap-2"
        >
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">
            {currentLocaleConfig?.shortName || 'PT'}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {i18nConfig.locales.map((localeOption) => {
          const config = LOCALE_CONFIG[localeOption];
          const isSelected = locale === localeOption;

          return (
            <DropdownMenuItem
              key={localeOption}
              onClick={() => setLocale(localeOption)}
              className="flex items-center justify-between cursor-pointer"
            >
              <div className="flex items-center gap-2">
                <span className="text-lg">{config.flag}</span>
                <span className="text-sm">{config.name}</span>
              </div>
              {isSelected && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
