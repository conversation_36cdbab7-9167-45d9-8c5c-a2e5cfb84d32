import { Server as NetServer } from 'http';

import { NextRequest, NextResponse } from 'next/server';
import { Server as SocketIOServer, Socket } from 'socket.io';

import { ActiveCollaborator } from '@/lib/collaboration/store';
import { conflictResolver, CellOperation } from '@/lib/collaboration/conflict-resolver';
import { wsAuthenticator } from '@/lib/collaboration/websocket-auth';
import { channelCleanup } from '@/lib/collaboration/channel-cleanup';
import { logger } from '@/lib/logger';
import { rateLimit, getClientIP } from '@/lib/rate-limiter';
import { prisma } from '@/server/db/client';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

// Definição do tipo para handshake.auth
interface SocketAuth {
  workbookId: string;
  userId: string;
  userName: string;
  userEmail?: string;
  token?: string; // JWT token para autenticação robusta
}

// Store para manter dados em memória
class CollaborationStore {
  private static instance: CollaborationStore;
  private activeCollaborators: Map<string, ActiveCollaborator[]> = new Map();
  private userSockets: Map<string, string> = new Map(); // userId -> socketId
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private workbookSockets: Map<string, Set<string>> = new Map(); // workbookId -> Set<socketId>

  private constructor() {}

  public static getInstance(): CollaborationStore {
    if (!CollaborationStore.instance) {
      CollaborationStore.instance = new CollaborationStore();
    }
    return CollaborationStore.instance;
  }

  // Adiciona um colaborador ativo
  public addCollaborator(workbookId: string, collaborator: ActiveCollaborator): void {
    // Obter lista atual ou criar nova
    const collaborators = this.activeCollaborators.get(workbookId) || [];

    // Verificar se já existe
    const existingIndex = collaborators.findIndex(c => c.id === collaborator.id);
    if (existingIndex >= 0) {
      // Atualizar existente
      collaborators[existingIndex] = collaborator;
    } else {
      // Adicionar novo
      collaborators.push(collaborator);
    }

    // Atualizar maps
    this.activeCollaborators.set(workbookId, collaborators);
    this.userSockets.set(collaborator.id, collaborator.socket);
    this.socketUsers.set(collaborator.socket, collaborator.id);

    // Adicionar à lista de sockets da planilha
    const workbookSocketsSet = this.workbookSockets.get(workbookId) || new Set();
    workbookSocketsSet.add(collaborator.socket);
    this.workbookSockets.set(workbookId, workbookSocketsSet);
  }

  // Remove um colaborador
  public removeCollaborator(socketId: string): string[] {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return [];

    const affectedWorkbooks: string[] = [];

    // Remover de todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const index = collaborators.findIndex(c => c.id === userId);
      if (index >= 0) {
        collaborators.splice(index, 1);
        this.activeCollaborators.set(workbookId, collaborators);
        affectedWorkbooks.push(workbookId);

        // Remover da lista de sockets da planilha
        const workbookSocketsSet = this.workbookSockets.get(workbookId);
        if (workbookSocketsSet) {
          workbookSocketsSet.delete(socketId);
          if (workbookSocketsSet.size === 0) {
            this.workbookSockets.delete(workbookId);
          } else {
            this.workbookSockets.set(workbookId, workbookSocketsSet);
          }
        }
      }
    });

    // Remover dos maps
    this.userSockets.delete(userId);
    this.socketUsers.delete(socketId);

    return affectedWorkbooks;
  }

  // Obtém colaboradores de uma planilha
  public getCollaborators(workbookId: string): ActiveCollaborator[] {
    return this.activeCollaborators.get(workbookId) || [];
  }

  // Atualiza posição do cursor
  public updateCollaboratorPosition(
    socketId: string,
    position: { row: number; col: number }
  ): { userId: string; workbookIds: string[] } | null {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return null;

    const updatedWorkbooks: string[] = [];

    // Atualizar em todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const collaborator = collaborators.find(c => c.id === userId);
      if (collaborator) {
        collaborator.position = position;
        collaborator.lastActive = new Date();
        collaborator.status = 'active';
        updatedWorkbooks.push(workbookId);
      }
    });

    return { userId, workbookIds: updatedWorkbooks };
  }

  // Obtém sockets de uma planilha
  public getWorkbookSockets(workbookId: string): string[] {
    const sockets = this.workbookSockets.get(workbookId);
    return sockets ? Array.from(sockets) : [];
  }
}

// Variável para armazenar a instância do socket.io
let io: SocketIOServer | null = null;

// Handler para rota de API
export async function GET(req: NextRequest, res: NextResponse) {
  try {
    // Verificar se o servidor já está inicializado
    if (!io) {
      // Obter o NetServer subjacente com tipo correto
      const resWithSocket = res as unknown as { socket: { server: NetServer } };
      const server: NetServer = resWithSocket.socket.server;

      // Inicializar o Socket.IO
      io = new SocketIOServer(server, {
        path: '/api/socket',
        cors: {
          origin: process.env.AUTH_NEXTAUTH_URL || '*',
          methods: ['GET', 'POST'],
          credentials: true,
        },
      });

      const store = CollaborationStore.getInstance();

      // Configurar manipuladores de eventos
      io.on('connection', async (socket: Socket) => {
        // Obter informações do usuário e planilha
        const auth = socket.handshake.auth as SocketAuth;
        const { workbookId, userId, userName, userEmail, token } = auth;
        const clientIP = getClientIP(socket.handshake as any);

        if (!workbookId || !userId) {
          socket.emit('error', 'Informações de autenticação incompletas');
          socket.disconnect();
          return;
        }

        // Rate limiting para conexões WebSocket
        const rateLimitResult = await rateLimit.websocketConnect(userId, clientIP);
        if (!rateLimitResult.allowed) {
          logger.warn('Rate limit excedido para conexão WebSocket', {
            userId,
            clientIP,
            remaining: rateLimitResult.remaining,
          });
          socket.emit('error', 'Muitas tentativas de conexão. Tente novamente em alguns minutos.');
          socket.disconnect();
          return;
        }

        // Autenticação JWT robusta (se token fornecido)
        if (token) {
          const authResult = await wsAuthenticator.validateWebSocketToken(token, socket.id);
          if (!authResult.success) {
            logger.warn('Falha na autenticação JWT WebSocket', {
              userId,
              error: authResult.error,
              shouldBlock: authResult.shouldBlock,
            });
            socket.emit('error', authResult.error || 'Falha na autenticação');
            socket.disconnect();
            return;
          }
        }

        // Verificar permissão para acessar a planilha
        try {
          // Usando o modelo de compartilhamento correto
          const workbook = await prisma.workbook.findFirst({
            where: {
              id: workbookId,
              OR: [
                { userId },
                {
                  shares: {
                    some: {
                      sharedWithUserId: userId,
                    },
                  },
                },
              ],
            },
          });

          if (!workbook) {
            socket.emit('error', 'Sem permissão para acessar esta planilha');
            socket.disconnect();
            return;
          }
        } catch (error) {
          logger.error('Erro ao verificar permissão de planilha:', error);
          socket.emit('error', 'Erro ao verificar permissão');
          socket.disconnect();
          return;
        }

        logger.info(`Usuário ${userName} (${userId}) conectado à planilha ${workbookId}`);

        // Juntar-se à sala da planilha
        socket.join(`workbook:${workbookId}`);

        // Registrar atividade no channel cleanup
        channelCleanup.registerChannelActivity(workbookId);

        // Adicionar colaborador à lista
        const collaborator: ActiveCollaborator = {
          id: userId,
          name: userName || 'Usuário',
          email: userEmail || '',
          socket: socket.id,
          status: 'active',
          lastActive: new Date(),
        };

        await store.addCollaborator(workbookId, collaborator);

        // Notificar outros usuários
        socket.to(`workbook:${workbookId}`).emit('collaborator_joined', {
          id: userId,
          name: userName,
          status: 'active',
        });

        // Enviar lista de colaboradores atuais para o usuário
        const collaborators = store.getCollaborators(workbookId);
        socket.emit('collaborators', collaborators);

        // Manipular posição do cursor com rate limiting
        socket.on('cursor_position', async (position: { row: number; col: number }) => {
          // Rate limiting para atualizações de cursor
          const cursorRateLimit = await rateLimit.cursorUpdate(userId, workbookId);
          if (!cursorRateLimit.allowed) {
            // Silenciosamente ignorar se rate limit excedido (cursor updates são frequentes)
            return;
          }

          const update = store.updateCollaboratorPosition(socket.id, position);
          if (update) {
            const { userId, workbookIds } = update;
            workbookIds.forEach(wbId => {
              socket.to(`workbook:${wbId}`).emit('cursor_position', {
                userId,
                position,
                timestamp: Date.now(),
              });
            });
          }
        });

        // Manipular alterações em células com resolução de conflitos
        socket.on('cell_changed', async (data: Record<string, unknown>) => {
          // Rate limiting para edições de células
          const cellEditRateLimit = await rateLimit.cellEdit(userId, workbookId);
          if (!cellEditRateLimit.allowed) {
            socket.emit('error', 'Muitas edições muito rapidamente. Aguarde um momento.');
            return;
          }

          try {
            // Criar operação para resolução de conflitos
            const operation: CellOperation = {
              id: `${Date.now()}_${userId}_${Math.random().toString(36).substr(2, 9)}`,
              type: 'update',
              cellId: data.cellId as string,
              sheetId: data.sheetId as string,
              value: data.value,
              oldValue: data.oldValue,
              timestamp: Date.now(),
              userId,
              userName: userName || 'Usuário',
            };

            // Resolver conflitos
            const resolution = await conflictResolver.addOperation(workbookId, operation);

            if (resolution.resolution === 'reject') {
              socket.emit('operation_rejected', {
                operationId: operation.id,
                reason: resolution.reason,
              });
              return;
            }

            // Preparar dados do evento
            const finalOperation = resolution.transformedOperation || operation;
            const eventData = {
              ...data,
              operationId: finalOperation.id,
              userId,
              userName,
              timestamp: finalOperation.timestamp,
              resolution: resolution.resolution,
            };

            // Transmitir para outros usuários
            socket.to(`workbook:${workbookId}`).emit('cell_changed', eventData);

            // Confirmar operação para o remetente
            socket.emit('operation_confirmed', {
              operationId: finalOperation.id,
              resolution: resolution.resolution,
            });

            // TODO: Persistir alterações no banco de dados se necessário
          } catch (error) {
            logger.error('Erro ao processar alteração de célula', {
              workbookId,
              userId,
              error: error instanceof Error ? error.message : 'Erro desconhecido',
            });
            socket.emit('error', 'Erro ao processar alteração');
          }
        });

        // Manipular desconexão
        socket.on('disconnect', async () => {
          // Remover colaborador e notificar outros usuários
          const affectedWorkbooks = await store.removeCollaborator(socket.id);

          affectedWorkbooks.forEach(wbId => {
            socket.to(`workbook:${wbId}`).emit('collaborator_left', userId);
          });

          // Remover da autenticação WebSocket
          if (token) {
            wsAuthenticator.removeActiveConnection(userId, socket.id);
          }

          // Limpeza de operações antigas do resolver de conflitos
          conflictResolver.cleanupOldOperations(workbookId, Date.now() - 60000); // 1 minuto

          // Verificar se não há mais colaboradores e desregistrar channel se necessário
          const remainingCollaborators = store.getCollaborators(workbookId);
          if (remainingCollaborators.length === 0) {
            channelCleanup.unregisterChannel(workbookId);
          }

          logger.info(`Usuário ${userName} (${userId}) desconectado`);
        });
      });
    }

    return new NextResponse('Socket.IO server is running', { status: 200 });
  } catch (error) {
    logger.error('Erro ao inicializar Socket.IO', error);
    return new NextResponse('Failed to start Socket.IO server', { status: 500 });
  }
}
