{"version": 3, "file": "supabase.js", "sources": ["../../../src/integrations/supabase.ts"], "sourcesContent": ["// Based on <PERSON><PERSON><PERSON>'s work on:\n// https://github.com/supabase-community/sentry-integration-js\n\n/* eslint-disable max-lines */\nimport { addBreadcrumb } from '../breadcrumbs';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { captureException } from '../exports';\nimport { defineIntegration } from '../integration';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '../semanticAttributes';\nimport { setHttpStatus, SPAN_STATUS_ERROR, SPAN_STATUS_OK, startSpan } from '../tracing';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport { isPlainObject } from '../utils-hoist/is';\nimport { logger } from '../utils-hoist/logger';\n\nconst AUTH_OPERATIONS_TO_INSTRUMENT = [\n  'reauthenticate',\n  'signInAnonymously',\n  'signInWithOAuth',\n  'signInWithIdToken',\n  'signInWithOtp',\n  'signInWithPassword',\n  'signInWithSSO',\n  'signOut',\n  'signUp',\n  'verifyOtp',\n];\n\nconst AUTH_ADMIN_OPERATIONS_TO_INSTRUMENT = [\n  'createUser',\n  'deleteUser',\n  'listUsers',\n  'getUserById',\n  'updateUserById',\n  'inviteUserByEmail',\n];\n\nexport const FILTER_MAPPINGS = {\n  eq: 'eq',\n  neq: 'neq',\n  gt: 'gt',\n  gte: 'gte',\n  lt: 'lt',\n  lte: 'lte',\n  like: 'like',\n  'like(all)': 'likeAllOf',\n  'like(any)': 'likeAnyOf',\n  ilike: 'ilike',\n  'ilike(all)': 'ilikeAllOf',\n  'ilike(any)': 'ilikeAnyOf',\n  is: 'is',\n  in: 'in',\n  cs: 'contains',\n  cd: 'containedBy',\n  sr: 'rangeGt',\n  nxl: 'rangeGte',\n  sl: 'rangeLt',\n  nxr: 'rangeLte',\n  adj: 'rangeAdjacent',\n  ov: 'overlaps',\n  fts: '',\n  plfts: 'plain',\n  phfts: 'phrase',\n  wfts: 'websearch',\n  not: 'not',\n};\n\nexport const DB_OPERATIONS_TO_INSTRUMENT = ['select', 'insert', 'upsert', 'update', 'delete'];\n\ntype AuthOperationFn = (...args: unknown[]) => Promise<unknown>;\ntype AuthOperationName = (typeof AUTH_OPERATIONS_TO_INSTRUMENT)[number];\ntype AuthAdminOperationName = (typeof AUTH_ADMIN_OPERATIONS_TO_INSTRUMENT)[number];\ntype PostgRESTQueryOperationFn = (...args: unknown[]) => PostgRESTFilterBuilder;\n\nexport interface SupabaseClientInstance {\n  auth: {\n    admin: Record<AuthAdminOperationName, AuthOperationFn>;\n  } & Record<AuthOperationName, AuthOperationFn>;\n}\n\nexport interface PostgRESTQueryBuilder {\n  [key: string]: PostgRESTQueryOperationFn;\n}\n\nexport interface PostgRESTFilterBuilder {\n  method: string;\n  headers: Record<string, string>;\n  url: URL;\n  schema: string;\n  body: any;\n}\n\nexport interface SupabaseResponse {\n  status?: number;\n  error?: {\n    message: string;\n    code?: string;\n    details?: unknown;\n  };\n}\n\nexport interface SupabaseError extends Error {\n  code?: string;\n  details?: unknown;\n}\n\nexport interface SupabaseBreadcrumb {\n  type: string;\n  category: string;\n  message: string;\n  data?: {\n    query?: string[];\n    body?: Record<string, unknown>;\n  };\n}\n\nexport interface SupabaseClientConstructor {\n  prototype: {\n    from: (table: string) => PostgRESTQueryBuilder;\n  };\n}\n\nexport interface PostgRESTProtoThenable {\n  then: <T>(\n    onfulfilled?: ((value: T) => T | PromiseLike<T>) | null,\n    onrejected?: ((reason: any) => T | PromiseLike<T>) | null,\n  ) => Promise<T>;\n}\n\ntype SentryInstrumented<T> = T & {\n  __SENTRY_INSTRUMENTED__?: boolean;\n};\n\nfunction markAsInstrumented<T>(fn: T): void {\n  try {\n    (fn as SentryInstrumented<T>).__SENTRY_INSTRUMENTED__ = true;\n  } catch {\n    // ignore errors here\n  }\n}\n\nfunction isInstrumented<T>(fn: T): boolean | undefined {\n  try {\n    return (fn as SentryInstrumented<T>).__SENTRY_INSTRUMENTED__;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Extracts the database operation type from the HTTP method and headers\n * @param method - The HTTP method of the request\n * @param headers - The request headers\n * @returns The database operation type ('select', 'insert', 'upsert', 'update', or 'delete')\n */\nexport function extractOperation(method: string, headers: Record<string, string> = {}): string {\n  switch (method) {\n    case 'GET': {\n      return 'select';\n    }\n    case 'POST': {\n      if (headers['Prefer']?.includes('resolution=')) {\n        return 'upsert';\n      } else {\n        return 'insert';\n      }\n    }\n    case 'PATCH': {\n      return 'update';\n    }\n    case 'DELETE': {\n      return 'delete';\n    }\n    default: {\n      return '<unknown-op>';\n    }\n  }\n}\n\n/**\n * Translates Supabase filter parameters into readable method names for tracing\n * @param key - The filter key from the URL search parameters\n * @param query - The filter value from the URL search parameters\n * @returns A string representation of the filter as a method call\n */\nexport function translateFiltersIntoMethods(key: string, query: string): string {\n  if (query === '' || query === '*') {\n    return 'select(*)';\n  }\n\n  if (key === 'select') {\n    return `select(${query})`;\n  }\n\n  if (key === 'or' || key.endsWith('.or')) {\n    return `${key}${query}`;\n  }\n\n  const [filter, ...value] = query.split('.');\n\n  let method;\n  // Handle optional `configPart` of the filter\n  if (filter?.startsWith('fts')) {\n    method = 'textSearch';\n  } else if (filter?.startsWith('plfts')) {\n    method = 'textSearch[plain]';\n  } else if (filter?.startsWith('phfts')) {\n    method = 'textSearch[phrase]';\n  } else if (filter?.startsWith('wfts')) {\n    method = 'textSearch[websearch]';\n  } else {\n    method = (filter && FILTER_MAPPINGS[filter as keyof typeof FILTER_MAPPINGS]) || 'filter';\n  }\n\n  return `${method}(${key}, ${value.join('.')})`;\n}\n\nfunction instrumentAuthOperation(operation: AuthOperationFn, isAdmin = false): AuthOperationFn {\n  return new Proxy(operation, {\n    apply(target, thisArg, argumentsList) {\n      return startSpan(\n        {\n          name: `auth ${isAdmin ? '(admin) ' : ''}${operation.name}`,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.db.supabase',\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'db',\n            'db.system': 'postgresql',\n            'db.operation': `auth.${isAdmin ? 'admin.' : ''}${operation.name}`,\n          },\n        },\n        span => {\n          return Reflect.apply(target, thisArg, argumentsList)\n            .then((res: unknown) => {\n              if (res && typeof res === 'object' && 'error' in res && res.error) {\n                span.setStatus({ code: SPAN_STATUS_ERROR });\n\n                captureException(res.error, {\n                  mechanism: {\n                    handled: false,\n                  },\n                });\n              } else {\n                span.setStatus({ code: SPAN_STATUS_OK });\n              }\n\n              span.end();\n              return res;\n            })\n            .catch((err: unknown) => {\n              span.setStatus({ code: SPAN_STATUS_ERROR });\n              span.end();\n\n              captureException(err, {\n                mechanism: {\n                  handled: false,\n                },\n              });\n\n              throw err;\n            })\n            .then(...argumentsList);\n        },\n      );\n    },\n  });\n}\n\nfunction instrumentSupabaseAuthClient(supabaseClientInstance: SupabaseClientInstance): void {\n  const auth = supabaseClientInstance.auth;\n\n  if (!auth || isInstrumented(supabaseClientInstance.auth)) {\n    return;\n  }\n\n  for (const operation of AUTH_OPERATIONS_TO_INSTRUMENT) {\n    const authOperation = auth[operation];\n\n    if (!authOperation) {\n      continue;\n    }\n\n    if (typeof supabaseClientInstance.auth[operation] === 'function') {\n      supabaseClientInstance.auth[operation] = instrumentAuthOperation(authOperation);\n    }\n  }\n\n  for (const operation of AUTH_ADMIN_OPERATIONS_TO_INSTRUMENT) {\n    const authOperation = auth.admin[operation];\n\n    if (!authOperation) {\n      continue;\n    }\n\n    if (typeof supabaseClientInstance.auth.admin[operation] === 'function') {\n      supabaseClientInstance.auth.admin[operation] = instrumentAuthOperation(authOperation, true);\n    }\n  }\n\n  markAsInstrumented(supabaseClientInstance.auth);\n}\n\nfunction instrumentSupabaseClientConstructor(SupabaseClient: unknown): void {\n  if (isInstrumented((SupabaseClient as unknown as SupabaseClientConstructor).prototype.from)) {\n    return;\n  }\n\n  (SupabaseClient as unknown as SupabaseClientConstructor).prototype.from = new Proxy(\n    (SupabaseClient as unknown as SupabaseClientConstructor).prototype.from,\n    {\n      apply(target, thisArg, argumentsList) {\n        const rv = Reflect.apply(target, thisArg, argumentsList);\n        const PostgRESTQueryBuilder = (rv as PostgRESTQueryBuilder).constructor;\n\n        instrumentPostgRESTQueryBuilder(PostgRESTQueryBuilder as unknown as new () => PostgRESTQueryBuilder);\n\n        return rv;\n      },\n    },\n  );\n\n  markAsInstrumented((SupabaseClient as unknown as SupabaseClientConstructor).prototype.from);\n}\n\nfunction instrumentPostgRESTFilterBuilder(PostgRESTFilterBuilder: PostgRESTFilterBuilder['constructor']): void {\n  if (isInstrumented((PostgRESTFilterBuilder.prototype as unknown as PostgRESTProtoThenable).then)) {\n    return;\n  }\n\n  (PostgRESTFilterBuilder.prototype as unknown as PostgRESTProtoThenable).then = new Proxy(\n    (PostgRESTFilterBuilder.prototype as unknown as PostgRESTProtoThenable).then,\n    {\n      apply(target, thisArg, argumentsList) {\n        const operations = DB_OPERATIONS_TO_INSTRUMENT;\n        const typedThis = thisArg as PostgRESTFilterBuilder;\n        const operation = extractOperation(typedThis.method, typedThis.headers);\n\n        if (!operations.includes(operation)) {\n          return Reflect.apply(target, thisArg, argumentsList);\n        }\n\n        if (!typedThis?.url?.pathname || typeof typedThis.url.pathname !== 'string') {\n          return Reflect.apply(target, thisArg, argumentsList);\n        }\n\n        const pathParts = typedThis.url.pathname.split('/');\n        const table = pathParts.length > 0 ? pathParts[pathParts.length - 1] : '';\n\n        const queryItems: string[] = [];\n        for (const [key, value] of typedThis.url.searchParams.entries()) {\n          // It's possible to have multiple entries for the same key, eg. `id=eq.7&id=eq.3`,\n          // so we need to use array instead of object to collect them.\n          queryItems.push(translateFiltersIntoMethods(key, value));\n        }\n        const body: Record<string, unknown> = Object.create(null);\n        if (isPlainObject(typedThis.body)) {\n          for (const [key, value] of Object.entries(typedThis.body)) {\n            body[key] = value;\n          }\n        }\n\n        // Adding operation to the beginning of the description if it's not a `select` operation\n        // For example, it can be an `insert` or `update` operation but the query can be `select(...)`\n        // For `select` operations, we don't need repeat it in the description\n        const description = `${operation === 'select' ? '' : `${operation}${body ? '(...) ' : ''}`}${queryItems.join(\n          ' ',\n        )} from(${table})`;\n\n        const attributes: Record<string, any> = {\n          'db.table': table,\n          'db.schema': typedThis.schema,\n          'db.url': typedThis.url.origin,\n          'db.sdk': typedThis.headers['X-Client-Info'],\n          'db.system': 'postgresql',\n          'db.operation': operation,\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.db.supabase',\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'db',\n        };\n\n        if (queryItems.length) {\n          attributes['db.query'] = queryItems;\n        }\n\n        if (Object.keys(body).length) {\n          attributes['db.body'] = body;\n        }\n\n        return startSpan(\n          {\n            name: description,\n            attributes,\n          },\n          span => {\n            return (Reflect.apply(target, thisArg, []) as Promise<SupabaseResponse>)\n              .then(\n                (res: SupabaseResponse) => {\n                  if (span) {\n                    if (res && typeof res === 'object' && 'status' in res) {\n                      setHttpStatus(span, res.status || 500);\n                    }\n                    span.end();\n                  }\n\n                  if (res.error) {\n                    const err = new Error(res.error.message) as SupabaseError;\n                    if (res.error.code) {\n                      err.code = res.error.code;\n                    }\n                    if (res.error.details) {\n                      err.details = res.error.details;\n                    }\n\n                    const supabaseContext: Record<string, unknown> = {};\n                    if (queryItems.length) {\n                      supabaseContext.query = queryItems;\n                    }\n                    if (Object.keys(body).length) {\n                      supabaseContext.body = body;\n                    }\n\n                    captureException(err, {\n                      contexts: {\n                        supabase: supabaseContext,\n                      },\n                    });\n                  }\n\n                  const breadcrumb: SupabaseBreadcrumb = {\n                    type: 'supabase',\n                    category: `db.${operation}`,\n                    message: description,\n                  };\n\n                  const data: Record<string, unknown> = {};\n\n                  if (queryItems.length) {\n                    data.query = queryItems;\n                  }\n\n                  if (Object.keys(body).length) {\n                    data.body = body;\n                  }\n\n                  if (Object.keys(data).length) {\n                    breadcrumb.data = data;\n                  }\n\n                  addBreadcrumb(breadcrumb);\n\n                  return res;\n                },\n                (err: Error) => {\n                  if (span) {\n                    setHttpStatus(span, 500);\n                    span.end();\n                  }\n                  throw err;\n                },\n              )\n              .then(...argumentsList);\n          },\n        );\n      },\n    },\n  );\n\n  markAsInstrumented((PostgRESTFilterBuilder.prototype as unknown as PostgRESTProtoThenable).then);\n}\n\nfunction instrumentPostgRESTQueryBuilder(PostgRESTQueryBuilder: new () => PostgRESTQueryBuilder): void {\n  // We need to wrap _all_ operations despite them sharing the same `PostgRESTFilterBuilder`\n  // constructor, as we don't know which method will be called first, and we don't want to miss any calls.\n  for (const operation of DB_OPERATIONS_TO_INSTRUMENT) {\n    if (isInstrumented((PostgRESTQueryBuilder.prototype as Record<string, any>)[operation])) {\n      continue;\n    }\n\n    type PostgRESTOperation = keyof Pick<PostgRESTQueryBuilder, 'select' | 'insert' | 'upsert' | 'update' | 'delete'>;\n    (PostgRESTQueryBuilder.prototype as Record<string, any>)[operation as PostgRESTOperation] = new Proxy(\n      (PostgRESTQueryBuilder.prototype as Record<string, any>)[operation as PostgRESTOperation],\n      {\n        apply(target, thisArg, argumentsList) {\n          const rv = Reflect.apply(target, thisArg, argumentsList);\n          const PostgRESTFilterBuilder = (rv as PostgRESTFilterBuilder).constructor;\n\n          DEBUG_BUILD && logger.log(`Instrumenting ${operation} operation's PostgRESTFilterBuilder`);\n\n          instrumentPostgRESTFilterBuilder(PostgRESTFilterBuilder);\n\n          return rv;\n        },\n      },\n    );\n\n    markAsInstrumented((PostgRESTQueryBuilder.prototype as Record<string, any>)[operation]);\n  }\n}\n\nexport const instrumentSupabaseClient = (supabaseClient: unknown): void => {\n  if (!supabaseClient) {\n    DEBUG_BUILD && logger.warn('Supabase integration was not installed because no Supabase client was provided.');\n    return;\n  }\n  const SupabaseClientConstructor =\n    supabaseClient.constructor === Function ? supabaseClient : supabaseClient.constructor;\n\n  instrumentSupabaseClientConstructor(SupabaseClientConstructor);\n  instrumentSupabaseAuthClient(supabaseClient as SupabaseClientInstance);\n};\n\nconst INTEGRATION_NAME = 'Supabase';\n\nconst _supabaseIntegration = ((supabaseClient: unknown) => {\n  return {\n    setupOnce() {\n      instrumentSupabaseClient(supabaseClient);\n    },\n    name: INTEGRATION_NAME,\n  };\n}) satisfies IntegrationFn;\n\nexport const supabaseIntegration = defineIntegration((options: { supabaseClient: any }) => {\n  return _supabaseIntegration(options.supabaseClient);\n}) satisfies IntegrationFn;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAaA,MAAM,gCAAgC;AACtC,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,EAAE,iBAAiB;AACnB,EAAE,mBAAmB;AACrB,EAAE,eAAe;AACjB,EAAE,oBAAoB;AACtB,EAAE,eAAe;AACjB,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,CAAC;;AAED,MAAM,sCAAsC;AAC5C,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,CAAC;;AAEM,MAAM,kBAAkB;AAC/B,EAAE,EAAE,EAAE,IAAI;AACV,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,EAAE,EAAE,IAAI;AACV,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,EAAE,EAAE,IAAI;AACV,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,WAAW,EAAE,WAAW;AAC1B,EAAE,WAAW,EAAE,WAAW;AAC1B,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,YAAY,EAAE,YAAY;AAC5B,EAAE,YAAY,EAAE,YAAY;AAC5B,EAAE,EAAE,EAAE,IAAI;AACV,EAAE,EAAE,EAAE,IAAI;AACV,EAAE,EAAE,EAAE,UAAU;AAChB,EAAE,EAAE,EAAE,aAAa;AACnB,EAAE,EAAE,EAAE,SAAS;AACf,EAAE,GAAG,EAAE,UAAU;AACjB,EAAE,EAAE,EAAE,SAAS;AACf,EAAE,GAAG,EAAE,UAAU;AACjB,EAAE,GAAG,EAAE,eAAe;AACtB,EAAE,EAAE,EAAE,UAAU;AAChB,EAAE,GAAG,EAAE,EAAE;AACT,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,GAAG,EAAE,KAAK;AACZ;;AAEa,MAAA,2BAAA,GAA8B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;;AAkE5F,SAAS,kBAAkB,CAAI,EAAE,EAAW;AAC5C,EAAE,IAAI;AACN,IAAI,CAAC,EAAG,GAA0B,uBAAA,GAA0B,IAAI;AAChE,IAAI,MAAM;AACV;AACA;AACA;;AAEA,SAAS,cAAc,CAAI,EAAE,EAA0B;AACvD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,EAAG,GAA0B,uBAAuB;AAChE,IAAI,MAAM;AACV,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAAU,OAAO,GAA2B,EAAE,EAAU;AAC/F,EAAE,QAAQ,MAAM;AAChB,IAAI,KAAK,KAAK,EAAE;AAChB,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,KAAK,MAAM,EAAE;AACjB,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE;AACtD,QAAQ,OAAO,QAAQ;AACvB,aAAa;AACb,QAAQ,OAAO,QAAQ;AACvB;AACA;AACA,IAAI,KAAK,OAAO,EAAE;AAClB,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,KAAK,QAAQ,EAAE;AACnB,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,SAAS;AACb,MAAM,OAAO,cAAc;AAC3B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,GAAG,EAAU,KAAK,EAAkB;AAChF,EAAE,IAAI,KAAM,KAAI,MAAM,KAAA,KAAU,GAAG,EAAE;AACrC,IAAI,OAAO,WAAW;AACtB;;AAEA,EAAE,IAAI,GAAI,KAAI,QAAQ,EAAE;AACxB,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7B;;AAEA,EAAE,IAAI,GAAI,KAAI,IAAK,IAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC3C,IAAI,OAAO,CAAC,EAAA,GAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AACA;;AAEA,EAAA,MAAA,CAAA,MAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AAEA,EAAA,IAAA,MAAA;AACA;AACA,EAAA,IAAA,MAAA,EAAA,UAAA,CAAA,KAAA,CAAA,EAAA;AACA,IAAA,MAAA,GAAA,YAAA;AACA,GAAA,MAAA,IAAA,MAAA,EAAA,UAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,GAAA,mBAAA;AACA,GAAA,MAAA,IAAA,MAAA,EAAA,UAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,GAAA,oBAAA;AACA,GAAA,MAAA,IAAA,MAAA,EAAA,UAAA,CAAA,MAAA,CAAA,EAAA;AACA,IAAA,MAAA,GAAA,uBAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA,GAAA,CAAA,MAAA,IAAA,eAAA,CAAA,MAAA,EAAA,KAAA,QAAA;AACA;;AAEA,EAAA,OAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,SAAA,uBAAA,CAAA,SAAA,EAAA,OAAA,GAAA,KAAA,EAAA;AACA,EAAA,OAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AACA,IAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA;AACA,MAAA,OAAA,SAAA;AACA,QAAA;AACA,UAAA,IAAA,EAAA,CAAA,KAAA,EAAA,OAAA,GAAA,UAAA,GAAA,EAAA,CAAA,EAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,UAAA,UAAA,EAAA;AACA,YAAA,CAAA,gCAAA,GAAA,kBAAA;AACA,YAAA,CAAA,4BAAA,GAAA,IAAA;AACA,YAAA,WAAA,EAAA,YAAA;AACA,YAAA,cAAA,EAAA,CAAA,KAAA,EAAA,OAAA,GAAA,QAAA,GAAA,EAAA,CAAA,EAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,QAAA,IAAA,IAAA;AACA,UAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA;AACA,aAAA,IAAA,CAAA,CAAA,GAAA,KAAA;AACA,cAAA,IAAA,GAAA,IAAA,OAAA,GAAA,KAAA,QAAA,IAAA,OAAA,IAAA,GAAA,IAAA,GAAA,CAAA,KAAA,EAAA;AACA,gBAAA,IAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,CAAA;;AAEA,gBAAA,gBAAA,CAAA,GAAA,CAAA,KAAA,EAAA;AACA,kBAAA,SAAA,EAAA;AACA,oBAAA,OAAA,EAAA,KAAA;AACA,mBAAA;AACA,iBAAA,CAAA;AACA,eAAA,MAAA;AACA,gBAAA,IAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,cAAA,EAAA,CAAA;AACA;;AAEA,cAAA,IAAA,CAAA,GAAA,EAAA;AACA,cAAA,OAAA,GAAA;AACA,aAAA;AACA,aAAA,KAAA,CAAA,CAAA,GAAA,KAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,CAAA;AACA,cAAA,IAAA,CAAA,GAAA,EAAA;;AAEA,cAAA,gBAAA,CAAA,GAAA,EAAA;AACA,gBAAA,SAAA,EAAA;AACA,kBAAA,OAAA,EAAA,KAAA;AACA,iBAAA;AACA,eAAA,CAAA;;AAEA,cAAA,MAAA,GAAA;AACA,aAAA;AACA,aAAA,IAAA,CAAA,GAAA,aAAA,CAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA;;AAEA,SAAA,4BAAA,CAAA,sBAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,sBAAA,CAAA,IAAA;;AAEA,EAAA,IAAA,CAAA,IAAA,IAAA,cAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,KAAA,MAAA,SAAA,IAAA,6BAAA,EAAA;AACA,IAAA,MAAA,aAAA,GAAA,IAAA,CAAA,SAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,MAAA;AACA;;AAEA,IAAA,IAAA,OAAA,sBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,UAAA,EAAA;AACA,MAAA,sBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,uBAAA,CAAA,aAAA,CAAA;AACA;AACA;;AAEA,EAAA,KAAA,MAAA,SAAA,IAAA,mCAAA,EAAA;AACA,IAAA,MAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,MAAA;AACA;;AAEA,IAAA,IAAA,OAAA,sBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,UAAA,EAAA;AACA,MAAA,sBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,uBAAA,CAAA,aAAA,EAAA,IAAA,CAAA;AACA;AACA;;AAEA,EAAA,kBAAA,CAAA,sBAAA,CAAA,IAAA,CAAA;AACA;;AAEA,SAAA,mCAAA,CAAA,cAAA,EAAA;AACA,EAAA,IAAA,cAAA,CAAA,CAAA,cAAA,GAAA,SAAA,CAAA,IAAA,CAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,CAAA,cAAA,GAAA,SAAA,CAAA,IAAA,GAAA,IAAA,KAAA;AACA,IAAA,CAAA,cAAA,GAAA,SAAA,CAAA,IAAA;AACA,IAAA;AACA,MAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA;AACA,QAAA,MAAA,EAAA,GAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,CAAA;AACA,QAAA,MAAA,qBAAA,GAAA,CAAA,EAAA,GAAA,WAAA;;AAEA,QAAA,+BAAA,CAAA,qBAAA,EAAA;;AAEA,QAAA,OAAA,EAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,kBAAA,CAAA,CAAA,cAAA,GAAA,SAAA,CAAA,IAAA,CAAA;AACA;;AAEA,SAAA,gCAAA,CAAA,sBAAA,EAAA;AACA,EAAA,IAAA,cAAA,CAAA,CAAA,sBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,CAAA,sBAAA,CAAA,SAAA,GAAA,IAAA,GAAA,IAAA,KAAA;AACA,IAAA,CAAA,sBAAA,CAAA,SAAA,GAAA,IAAA;AACA,IAAA;AACA,MAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA;AACA,QAAA,MAAA,UAAA,GAAA,2BAAA;AACA,QAAA,MAAA,SAAA,GAAA,OAAA;AACA,QAAA,MAAA,SAAA,GAAA,gBAAA,CAAA,SAAA,CAAA,MAAA,EAAA,SAAA,CAAA,OAAA,CAAA;;AAEA,QAAA,IAAA,CAAA,UAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA;AACA,UAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,CAAA;AACA;;AAEA,QAAA,IAAA,CAAA,SAAA,EAAA,GAAA,EAAA,QAAA,IAAA,OAAA,SAAA,CAAA,GAAA,CAAA,QAAA,KAAA,QAAA,EAAA;AACA,UAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,CAAA;AACA;;AAEA,QAAA,MAAA,SAAA,GAAA,SAAA,CAAA,GAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,QAAA,MAAA,KAAA,GAAA,SAAA,CAAA,MAAA,GAAA,CAAA,GAAA,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA,GAAA,EAAA;;AAEA,QAAA,MAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,SAAA,CAAA,GAAA,CAAA,YAAA,CAAA,OAAA,EAAA,EAAA;AACA;AACA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA,2BAAA,CAAA,GAAA,EAAA,KAAA,CAAA,CAAA;AACA;AACA,QAAA,MAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,aAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,MAAA,CAAA,OAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,CAAA,GAAA,KAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAA,MAAA,WAAA,GAAA,CAAA,EAAA,SAAA,KAAA,QAAA,GAAA,EAAA,GAAA,CAAA,EAAA,SAAA,CAAA,EAAA,IAAA,GAAA,QAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,IAAA;AACA,UAAA,GAAA;AACA,SAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAEA,QAAA,MAAA,UAAA,GAAA;AACA,UAAA,UAAA,EAAA,KAAA;AACA,UAAA,WAAA,EAAA,SAAA,CAAA,MAAA;AACA,UAAA,QAAA,EAAA,SAAA,CAAA,GAAA,CAAA,MAAA;AACA,UAAA,QAAA,EAAA,SAAA,CAAA,OAAA,CAAA,eAAA,CAAA;AACA,UAAA,WAAA,EAAA,YAAA;AACA,UAAA,cAAA,EAAA,SAAA;AACA,UAAA,CAAA,gCAAA,GAAA,kBAAA;AACA,UAAA,CAAA,4BAAA,GAAA,IAAA;AACA,SAAA;;AAEA,QAAA,IAAA,UAAA,CAAA,MAAA,EAAA;AACA,UAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAA;AACA;;AAEA,QAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA;AACA,UAAA,UAAA,CAAA,SAAA,CAAA,GAAA,IAAA;AACA;;AAEA,QAAA,OAAA,SAAA;AACA,UAAA;AACA,YAAA,IAAA,EAAA,WAAA;AACA,YAAA,UAAA;AACA,WAAA;AACA,UAAA,IAAA,IAAA;AACA,YAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,CAAA;AACA,eAAA,IAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA,kBAAA,IAAA,IAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,OAAA,GAAA,KAAA,QAAA,IAAA,QAAA,IAAA,GAAA,EAAA;AACA,sBAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA,MAAA,IAAA,GAAA,CAAA;AACA;AACA,oBAAA,IAAA,CAAA,GAAA,EAAA;AACA;;AAEA,kBAAA,IAAA,GAAA,CAAA,KAAA,EAAA;AACA,oBAAA,MAAA,GAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,OAAA,CAAA;AACA,oBAAA,IAAA,GAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA,CAAA,IAAA;AACA;AACA,oBAAA,IAAA,GAAA,CAAA,KAAA,CAAA,OAAA,EAAA;AACA,sBAAA,GAAA,CAAA,OAAA,GAAA,GAAA,CAAA,KAAA,CAAA,OAAA;AACA;;AAEA,oBAAA,MAAA,eAAA,GAAA,EAAA;AACA,oBAAA,IAAA,UAAA,CAAA,MAAA,EAAA;AACA,sBAAA,eAAA,CAAA,KAAA,GAAA,UAAA;AACA;AACA,oBAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA;AACA,sBAAA,eAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AAEA,oBAAA,gBAAA,CAAA,GAAA,EAAA;AACA,sBAAA,QAAA,EAAA;AACA,wBAAA,QAAA,EAAA,eAAA;AACA,uBAAA;AACA,qBAAA,CAAA;AACA;;AAEA,kBAAA,MAAA,UAAA,GAAA;AACA,oBAAA,IAAA,EAAA,UAAA;AACA,oBAAA,QAAA,EAAA,CAAA,GAAA,EAAA,SAAA,CAAA,CAAA;AACA,oBAAA,OAAA,EAAA,WAAA;AACA,mBAAA;;AAEA,kBAAA,MAAA,IAAA,GAAA,EAAA;;AAEA,kBAAA,IAAA,UAAA,CAAA,MAAA,EAAA;AACA,oBAAA,IAAA,CAAA,KAAA,GAAA,UAAA;AACA;;AAEA,kBAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA;AACA,oBAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AAEA,kBAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA;AACA,oBAAA,UAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AAEA,kBAAA,aAAA,CAAA,UAAA,CAAA;;AAEA,kBAAA,OAAA,GAAA;AACA,iBAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA,kBAAA,IAAA,IAAA,EAAA;AACA,oBAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,oBAAA,IAAA,CAAA,GAAA,EAAA;AACA;AACA,kBAAA,MAAA,GAAA;AACA,iBAAA;AACA;AACA,eAAA,IAAA,CAAA,GAAA,aAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,kBAAA,CAAA,CAAA,sBAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AACA;;AAEA,SAAA,+BAAA,CAAA,qBAAA,EAAA;AACA;AACA;AACA,EAAA,KAAA,MAAA,SAAA,IAAA,2BAAA,EAAA;AACA,IAAA,IAAA,cAAA,CAAA,CAAA,qBAAA,CAAA,SAAA,GAAA,SAAA,CAAA,CAAA,EAAA;AACA,MAAA;AACA;;AAGA,IAAA,CAAA,qBAAA,CAAA,SAAA,GAAA,SAAA,EAAA,GAAA,IAAA,KAAA;AACA,MAAA,CAAA,qBAAA,CAAA,SAAA,GAAA,SAAA,EAAA;AACA,MAAA;AACA,QAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,EAAA,GAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,aAAA,CAAA;AACA,UAAA,MAAA,sBAAA,GAAA,CAAA,EAAA,GAAA,WAAA;;AAEA,UAAA,WAAA,IAAA,MAAA,CAAA,GAAA,CAAA,CAAA,cAAA,EAAA,SAAA,CAAA,mCAAA,CAAA,CAAA;;AAEA,UAAA,gCAAA,CAAA,sBAAA,CAAA;;AAEA,UAAA,OAAA,EAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;;AAEA,IAAA,kBAAA,CAAA,CAAA,qBAAA,CAAA,SAAA,GAAA,SAAA,CAAA,CAAA;AACA;AACA;;AAEA,MAAA,wBAAA,GAAA,CAAA,cAAA,KAAA;AACA,EAAA,IAAA,CAAA,cAAA,EAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,IAAA,CAAA,iFAAA,CAAA;AACA,IAAA;AACA;AACA,EAAA,MAAA,yBAAA;AACA,IAAA,cAAA,CAAA,WAAA,KAAA,QAAA,GAAA,cAAA,GAAA,cAAA,CAAA,WAAA;;AAEA,EAAA,mCAAA,CAAA,yBAAA,CAAA;AACA,EAAA,4BAAA,CAAA,cAAA,EAAA;AACA;;AAEA,MAAA,gBAAA,GAAA,UAAA;;AAEA,MAAA,oBAAA,IAAA,CAAA,cAAA,KAAA;AACA,EAAA,OAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,wBAAA,CAAA,cAAA,CAAA;AACA,KAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAEA,MAAA,mBAAA,GAAA,iBAAA,CAAA,CAAA,OAAA,KAAA;AACA,EAAA,OAAA,oBAAA,CAAA,OAAA,CAAA,cAAA,CAAA;AACA,CAAA,CAAA;;;;"}