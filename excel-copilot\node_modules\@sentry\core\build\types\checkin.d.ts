import type { SerializedCheckIn } from './types-hoist/checkin';
import type { DsnComponents } from './types-hoist/dsn';
import type { CheckInEnvelope, DynamicSamplingContext } from './types-hoist/envelope';
import type { SdkMetadata } from './types-hoist/sdkmetadata';
/**
 * Create envelope from check in item.
 */
export declare function createCheckInEnvelope(checkIn: SerializedCheckIn, dynamicSamplingContext?: Partial<DynamicSamplingContext>, metadata?: SdkMetadata, tunnel?: string, dsn?: DsnComponents): CheckInEnvelope;
//# sourceMappingURL=checkin.d.ts.map